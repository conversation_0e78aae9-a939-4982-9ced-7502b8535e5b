{"name": "spt-frontend", "version": "1.0.0", "description": "Blockchain Security Protocol Tool - Frontend Dashboard", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "rxjs": "~7.8.1", "tslib": "^2.8.0", "zone.js": "~0.15.0", "chart.js": "^4.5.0", "ng2-charts": "^6.0.0", "prismjs": "^1.29.0", "ngx-markdown": "^19.0.0", "jsonwebtoken": "^9.0.2", "@types/jsonwebtoken": "^9.0.7", "socket.io-client": "^4.8.1", "date-fns": "^4.1.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.0"}}