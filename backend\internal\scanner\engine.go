package scanner

import (
	"context"
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/pkg/bitcoin"
	"blockchain-spt/spt/backend/pkg/ethereum"
	"blockchain-spt/spt/backend/pkg/security"

	"github.com/sirupsen/logrus"
)

// Engine represents the main security scanning engine
type Engine struct {
	config          *config.Config
	ethereumScanner *ethereum.Scanner
	bitcoinScanner  *bitcoin.Scanner
	securityScanner *security.Scanner
	logger          *logrus.Logger
	mu              sync.RWMutex
	scanHistory     []models.ScanResult
}

// NewEngine creates a new scanner engine instance
func NewEngine(cfg *config.Config) (*Engine, error) {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Initialize blockchain-specific scanners
	ethScanner, err := ethereum.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Ethereum scanner: %w", err)
	}

	btcScanner, err := bitcoin.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Bitcoin scanner: %w", err)
	}

	secScanner, err := security.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize security scanner: %w", err)
	}

	return &Engine{
		config:          cfg,
		ethereumScanner: ethScanner,
		bitcoinScanner:  btcScanner,
		securityScanner: secScanner,
		logger:          logger,
		scanHistory:     make([]models.ScanResult, 0),
	}, nil
}

// ScanProject performs a comprehensive security scan of a project
func (e *Engine) ScanProject(ctx context.Context, projectPath string, chains []string) (*models.ScanResult, error) {
	e.logger.Infof("Starting security scan for project: %s", projectPath)

	startTime := time.Now()
	result := &models.ScanResult{
		ID:          generateScanID(),
		ProjectPath: projectPath,
		Chains:      chains,
		StartTime:   startTime,
		Status:      models.ScanStatusRunning,
		Issues:      make([]models.SecurityIssue, 0),
	}

	// Store scan result
	e.mu.Lock()
	e.scanHistory = append(e.scanHistory, *result)
	e.mu.Unlock()

	var allIssues []models.SecurityIssue

	// Run general security scans
	securityIssues, err := e.securityScanner.ScanProject(ctx, projectPath)
	if err != nil {
		e.logger.Errorf("Security scan failed: %v", err)
		result.Status = models.ScanStatusFailed
		result.Error = err.Error()
		return result, err
	}
	allIssues = append(allIssues, securityIssues...)

	// Run blockchain-specific scans
	for _, chain := range chains {
		switch chain {
		case "ethereum":
			if e.config.Plugins.Ethereum.Enabled {
				ethIssues, err := e.ethereumScanner.ScanProject(ctx, projectPath)
				if err != nil {
					e.logger.Errorf("Ethereum scan failed: %v", err)
					continue
				}
				allIssues = append(allIssues, ethIssues...)
			}
		case "bitcoin":
			if e.config.Plugins.Bitcoin.Enabled {
				btcIssues, err := e.bitcoinScanner.ScanProject(ctx, projectPath)
				if err != nil {
					e.logger.Errorf("Bitcoin scan failed: %v", err)
					continue
				}
				allIssues = append(allIssues, btcIssues...)
			}
		}
	}

	// Update result
	result.Issues = allIssues
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Status = models.ScanStatusCompleted

	// Calculate severity counts
	result.SeverityCounts = calculateSeverityCounts(allIssues)

	e.logger.Infof("Scan completed for project: %s, found %d issues", projectPath, len(allIssues))

	return result, nil
}

// ScanFile performs security scan on a specific file
func (e *Engine) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	e.logger.Infof("Scanning file: %s", filePath)

	var allIssues []models.SecurityIssue

	// Determine file type and run appropriate scanners
	ext := filepath.Ext(filePath)

	// Always run general security scans
	securityIssues, err := e.securityScanner.ScanFile(ctx, filePath)
	if err != nil {
		e.logger.Errorf("Security scan failed for file %s: %v", filePath, err)
	} else {
		allIssues = append(allIssues, securityIssues...)
	}

	// Run blockchain-specific scans based on file extension
	switch ext {
	case ".sol":
		if e.config.Plugins.Ethereum.Enabled {
			ethIssues, err := e.ethereumScanner.ScanFile(ctx, filePath)
			if err != nil {
				e.logger.Errorf("Ethereum scan failed for file %s: %v", filePath, err)
			} else {
				allIssues = append(allIssues, ethIssues...)
			}
		}
	case ".js", ".ts":
		// Check if it's a Bitcoin-related script
		if e.config.Plugins.Bitcoin.Enabled {
			btcIssues, err := e.bitcoinScanner.ScanFile(ctx, filePath)
			if err != nil {
				e.logger.Errorf("Bitcoin scan failed for file %s: %v", filePath, err)
			} else {
				allIssues = append(allIssues, btcIssues...)
			}
		}
	}

	return allIssues, nil
}

// GetScanHistory returns the scan history
func (e *Engine) GetScanHistory() []models.ScanResult {
	e.mu.RLock()
	defer e.mu.RUnlock()

	// Return a copy to prevent external modification
	history := make([]models.ScanResult, len(e.scanHistory))
	copy(history, e.scanHistory)
	return history
}

// GetScanResult returns a specific scan result by ID
func (e *Engine) GetScanResult(scanID string) (*models.ScanResult, error) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	for _, result := range e.scanHistory {
		if result.ID == scanID {
			return &result, nil
		}
	}

	return nil, fmt.Errorf("scan result not found: %s", scanID)
}

// calculateSeverityCounts calculates the count of issues by severity
func calculateSeverityCounts(issues []models.SecurityIssue) map[string]int {
	counts := map[string]int{
		"critical": 0,
		"high":     0,
		"medium":   0,
		"low":      0,
		"info":     0,
	}

	for _, issue := range issues {
		if count, exists := counts[issue.Severity]; exists {
			counts[issue.Severity] = count + 1
		}
	}

	return counts
}

// generateScanID generates a unique scan ID
func generateScanID() string {
	return fmt.Sprintf("scan_%d", time.Now().UnixNano())
}
