import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="error-container">
      <mat-card class="error-card">
        <mat-card-content>
          <div class="error-content">
            <mat-icon class="error-icon">block</mat-icon>
            <h1>Access Denied</h1>
            <h2>401 - Unauthorized</h2>
            <p>You don't have permission to access this resource.</p>
            <p>Please contact your administrator if you believe this is an error.</p>
            
            <div class="actions">
              <button mat-raised-button color="primary" routerLink="/dashboard">
                <mat-icon>home</mat-icon>
                Go to Dashboard
              </button>
              <button mat-button routerLink="/login">
                <mat-icon>login</mat-icon>
                Login Again
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .error-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .error-card {
      max-width: 500px;
      width: 100%;
      text-align: center;
    }

    .error-content {
      padding: 40px 20px;
    }

    .error-icon {
      font-size: 72px;
      width: 72px;
      height: 72px;
      color: #f44336;
      margin-bottom: 20px;
    }

    h1 {
      color: #333;
      margin-bottom: 10px;
      font-weight: 300;
    }

    h2 {
      color: #666;
      margin-bottom: 20px;
      font-weight: 400;
    }

    p {
      color: #666;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .actions {
      margin-top: 30px;
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .actions button {
      min-width: 140px;
    }

    @media (max-width: 480px) {
      .actions {
        flex-direction: column;
        align-items: center;
      }

      .actions button {
        width: 100%;
        max-width: 200px;
      }
    }
  `]
})
export class UnauthorizedComponent {
}
