package api

import (
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, handler *Handler) {
	// API version 1
	v1 := router.Group("/api/v1")
	{
		// Health check
		v1.GET("/health", handler.HealthCheck)

		// Scanning endpoints
		scan := v1.Group("/scan")
		{
			scan.POST("/start", handler.StartScan)
			scan.GET("/result/:scanId", handler.GetScanResult)
			scan.GET("/history", handler.GetScanHistory)
			scan.GET("/file", handler.ScanFile)
		}

		// Reporting endpoints
		report := v1.Group("/report")
		{
			report.POST("/generate", handler.GenerateReport)
		}

		// Security checklist endpoints
		checklist := v1.Group("/checklist")
		{
			checklist.GET("/", handler.GetSecurityChecklist)
		}

		// Configuration endpoints
		config := v1.Group("/config")
		{
			config.GET("/", handler.GetConfiguration)
			config.PUT("/", handler.UpdateConfiguration)
		}
	}

	// WebSocket endpoints for real-time updates
	router.GET("/ws", handler.HandleWebSocket)

	// Static file serving for frontend
	router.Static("/static", "./frontend/dist")
	router.StaticFile("/", "./frontend/dist/index.html")
}

// HandleWebSocket handles WebSocket connections for real-time updates
func (h *Handler) HandleWebSocket(c *gin.Context) {
	// WebSocket implementation would go here
	// For now, return a placeholder response
	c.JSON(200, gin.H{
		"message": "WebSocket endpoint - implementation pending",
	})
}
