package dependencies

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"

	"github.com/sirupsen/logrus"
)

// EnvironmentScanner scans for environment and configuration security issues
type EnvironmentScanner struct {
	logger *logrus.Logger
}

// EnvironmentIssue represents an environment-specific security issue
type EnvironmentIssue struct {
	Type        string
	Severity    string
	Description string
	File        string
	Line        int
	Code        string
	Suggestion  string
}

// NewEnvironmentScanner creates a new environment scanner
func NewEnvironmentScanner() *EnvironmentScanner {
	return &EnvironmentScanner{
		logger: logrus.New(),
	}
}

// ScanEnvironment scans the entire project for environment security issues
func (es *EnvironmentScanner) ScanEnvironment(projectPath string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// This would walk through the project and scan environment files
	// For now, return empty slice
	return issues, nil
}

// ScanFile scans a specific file for environment security issues
func (es *EnvironmentScanner) ScanFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	fileName := strings.ToLower(filePath)

	// Determine file type and apply appropriate checks
	if es.isEnvFile(fileName) {
		envIssues := es.scanEnvFile(filePath, content)
		issues = append(issues, envIssues...)
	}

	if es.isDockerFile(fileName) {
		dockerIssues := es.scanDockerFile(filePath, content)
		issues = append(issues, dockerIssues...)
	}

	if es.isConfigFile(fileName) {
		configIssues := es.scanConfigFile(filePath, content)
		issues = append(issues, configIssues...)
	}

	if es.isKubernetesFile(fileName) {
		k8sIssues := es.scanKubernetesFile(filePath, content)
		issues = append(issues, k8sIssues...)
	}

	return issues, nil
}

// scanEnvFile scans environment files (.env, etc.)
func (es *EnvironmentScanner) scanEnvFile(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, es.checkHardcodedSecrets(filePath, i+1, line)...)

		// Check for weak passwords
		issues = append(issues, es.checkWeakPasswords(filePath, i+1, line)...)

		// Check for insecure URLs
		issues = append(issues, es.checkInsecureURLs(filePath, i+1, line)...)

		// Check for debug settings
		issues = append(issues, es.checkDebugSettings(filePath, i+1, line)...)

		// Check for default credentials
		issues = append(issues, es.checkDefaultCredentials(filePath, i+1, line)...)
	}

	return issues
}

// scanDockerFile scans Dockerfile for security issues
func (es *EnvironmentScanner) scanDockerFile(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for running as root
		if strings.HasPrefix(strings.ToUpper(line), "USER ROOT") ||
			(strings.HasPrefix(strings.ToUpper(line), "USER") && strings.Contains(line, "0")) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("docker_root_user_%d", i+1),
				Type:        "docker_root_user",
				Severity:    "high",
				Title:       "Docker Container Running as Root",
				Description: "Container is configured to run as root user",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Create and use a non-root user in the container",
				References:  []string{"https://docs.docker.com/develop/dev-best-practices/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for ADD instead of COPY
		if strings.HasPrefix(strings.ToUpper(line), "ADD ") && !strings.Contains(line, "http") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("docker_add_command_%d", i+1),
				Type:        "docker_add_command",
				Severity:    "low",
				Title:       "Use COPY Instead of ADD",
				Description: "ADD command has additional features that may be unnecessary",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Use COPY instead of ADD for local files",
				References:  []string{"https://docs.docker.com/develop/dev-best-practices/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for latest tag
		if strings.Contains(strings.ToUpper(line), "FROM") && strings.Contains(line, ":latest") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("docker_latest_tag_%d", i+1),
				Type:        "docker_latest_tag",
				Severity:    "medium",
				Title:       "Using Latest Tag in Docker Image",
				Description: "Using 'latest' tag can lead to unpredictable builds",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Pin to specific version tags",
				References:  []string{"https://docs.docker.com/develop/dev-best-practices/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for secrets in environment variables
		if strings.HasPrefix(strings.ToUpper(line), "ENV ") {
			envLine := strings.TrimPrefix(strings.ToUpper(line), "ENV ")
			issues = append(issues, es.checkHardcodedSecrets(filePath, i+1, envLine)...)
		}
	}

	return issues
}

// scanConfigFile scans configuration files for security issues
func (es *EnvironmentScanner) scanConfigFile(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, "//") {
			continue
		}

		// Check for hardcoded secrets in JSON/YAML configs
		issues = append(issues, es.checkHardcodedSecrets(filePath, i+1, line)...)

		// Check for debug mode enabled
		if es.isDebugEnabled(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("config_debug_enabled_%d", i+1),
				Type:        "debug_enabled",
				Severity:    "medium",
				Title:       "Debug Mode Enabled",
				Description: "Debug mode is enabled in configuration",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Disable debug mode in production",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure SSL/TLS settings
		if es.isInsecureSSL(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("config_insecure_ssl_%d", i+1),
				Type:        "insecure_ssl",
				Severity:    "high",
				Title:       "Insecure SSL/TLS Configuration",
				Description: "SSL/TLS verification is disabled or weakened",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Enable proper SSL/TLS verification",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanKubernetesFile scans Kubernetes manifests for security issues
func (es *EnvironmentScanner) scanKubernetesFile(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for privileged containers
		if strings.Contains(line, "privileged: true") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("k8s_privileged_%d", i+1),
				Type:        "k8s_privileged_container",
				Severity:    "critical",
				Title:       "Privileged Container",
				Description: "Container is running in privileged mode",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Remove privileged mode unless absolutely necessary",
				References:  []string{"https://kubernetes.io/docs/concepts/policy/pod-security-policy/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for host network
		if strings.Contains(line, "hostNetwork: true") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("k8s_host_network_%d", i+1),
				Type:        "k8s_host_network",
				Severity:    "high",
				Title:       "Host Network Access",
				Description: "Pod has access to host network",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Avoid using host network unless required",
				References:  []string{"https://kubernetes.io/docs/concepts/policy/pod-security-policy/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for root user
		if strings.Contains(line, "runAsUser: 0") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("k8s_root_user_%d", i+1),
				Type:        "k8s_root_user",
				Severity:    "high",
				Title:       "Container Running as Root",
				Description: "Container is configured to run as root user",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Run containers as non-root user",
				References:  []string{"https://kubernetes.io/docs/concepts/policy/pod-security-policy/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkHardcodedSecrets checks for hardcoded secrets in a line
func (es *EnvironmentScanner) checkHardcodedSecrets(filePath string, lineNum int, line string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	secretPatterns := []struct {
		pattern     string
		description string
		severity    string
	}{
		{`(?i)(password|passwd|pwd)\s*[:=]\s*["']?[^"'\s]{8,}["']?`, "Hardcoded password", "critical"},
		{`(?i)(api[_-]?key|apikey)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded API key", "critical"},
		{`(?i)(secret[_-]?key|secretkey)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded secret key", "critical"},
		{`(?i)(access[_-]?token|accesstoken)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded access token", "critical"},
		{`(?i)(private[_-]?key|privatekey)\s*[:=]\s*["']?[^"'\s]{32,}["']?`, "Hardcoded private key", "critical"},
		{`(?i)(database[_-]?url|db[_-]?url)\s*[:=]\s*["']?[^"'\s]+["']?`, "Hardcoded database URL", "high"},
		{`(?i)(jwt[_-]?secret|jwtsecret)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded JWT secret", "critical"},
		{`(?i)(encryption[_-]?key|encryptionkey)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded encryption key", "critical"},
	}

	for _, pattern := range secretPatterns {
		if matched, _ := regexp.MatchString(pattern.pattern, line); matched {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("env_secret_%d_%s", lineNum, pattern.description),
				Type:        "hardcoded_secret",
				Severity:    pattern.severity,
				Title:       "Hardcoded Secret",
				Description: pattern.description + " found in environment file",
				File:        filePath,
				Line:        lineNum,
				Code:        es.maskSecrets(line),
				Chain:       "general",
				Category:    "environment",
				CWE:         "CWE-798",
				OWASP:       "A02:2021",
				Suggestion:  "Use environment variables or secure secret management",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkWeakPasswords checks for weak passwords
func (es *EnvironmentScanner) checkWeakPasswords(filePath string, lineNum int, line string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	weakPasswords := []string{
		"password", "123456", "admin", "root", "test", "guest",
		"password123", "admin123", "qwerty", "letmein", "welcome",
	}

	passwordPattern := regexp.MustCompile(`(?i)(password|passwd|pwd)\s*[:=]\s*["']?([^"'\s]+)["']?`)
	matches := passwordPattern.FindStringSubmatch(line)

	if len(matches) > 2 {
		password := strings.ToLower(matches[2])
		for _, weak := range weakPasswords {
			if password == weak || len(password) < 8 {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("env_weak_password_%d", lineNum),
					Type:        "weak_password",
					Severity:    "high",
					Title:       "Weak Password",
					Description: "Weak or default password detected",
					File:        filePath,
					Line:        lineNum,
					Code:        es.maskSecrets(line),
					Chain:       "general",
					Category:    "environment",
					Suggestion:  "Use strong, randomly generated passwords",
					References:  []string{"https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
				break
			}
		}
	}

	return issues
}

// checkInsecureURLs checks for insecure URLs
func (es *EnvironmentScanner) checkInsecureURLs(filePath string, lineNum int, line string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	if strings.Contains(line, "http://") && !strings.Contains(line, "localhost") && !strings.Contains(line, "127.0.0.1") {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("env_insecure_url_%d", lineNum),
			Type:        "insecure_url",
			Severity:    "medium",
			Title:       "Insecure HTTP URL",
			Description: "HTTP URL detected, should use HTTPS",
			File:        filePath,
			Line:        lineNum,
			Code:        line,
			Chain:       "general",
			Category:    "environment",
			Suggestion:  "Use HTTPS URLs for secure communication",
			References:  []string{"https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// checkDebugSettings checks for debug settings
func (es *EnvironmentScanner) checkDebugSettings(filePath string, lineNum int, line string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	debugPatterns := []string{
		`(?i)debug\s*[:=]\s*true`,
		`(?i)debug[_-]?mode\s*[:=]\s*true`,
		`(?i)development\s*[:=]\s*true`,
		`(?i)verbose\s*[:=]\s*true`,
	}

	for _, pattern := range debugPatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("env_debug_%d", lineNum),
				Type:        "debug_enabled",
				Severity:    "medium",
				Title:       "Debug Mode Enabled",
				Description: "Debug mode is enabled which may expose sensitive information",
				File:        filePath,
				Line:        lineNum,
				Code:        line,
				Chain:       "general",
				Category:    "environment",
				Suggestion:  "Disable debug mode in production environments",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
			break
		}
	}

	return issues
}

// checkDefaultCredentials checks for default credentials
func (es *EnvironmentScanner) checkDefaultCredentials(filePath string, lineNum int, line string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	defaultCreds := map[string]string{
		"admin":    "admin",
		"root":     "root",
		"user":     "user",
		"test":     "test",
		"guest":    "guest",
		"postgres": "postgres",
		"mysql":    "mysql",
		"redis":    "redis",
	}

	for username, password := range defaultCreds {
		userPattern := fmt.Sprintf(`(?i)user(?:name)?\s*[:=]\s*["']?%s["']?`, username)
		passPattern := fmt.Sprintf(`(?i)pass(?:word)?\s*[:=]\s*["']?%s["']?`, password)

		if matched, _ := regexp.MatchString(userPattern, line); matched {
			if matched, _ := regexp.MatchString(passPattern, line); matched {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("env_default_creds_%d", lineNum),
					Type:        "default_credentials",
					Severity:    "critical",
					Title:       "Default Credentials",
					Description: "Default username/password combination detected",
					File:        filePath,
					Line:        lineNum,
					Code:        es.maskSecrets(line),
					Chain:       "general",
					Category:    "environment",
					Suggestion:  "Change default credentials immediately",
					References:  []string{"https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// Helper functions

func (es *EnvironmentScanner) isEnvFile(fileName string) bool {
	envFiles := []string{".env", ".env.local", ".env.development", ".env.production", ".env.test"}
	for _, envFile := range envFiles {
		if strings.Contains(fileName, envFile) {
			return true
		}
	}
	return false
}

func (es *EnvironmentScanner) isDockerFile(fileName string) bool {
	return strings.Contains(fileName, "dockerfile") || fileName == "dockerfile"
}

func (es *EnvironmentScanner) isConfigFile(fileName string) bool {
	configFiles := []string{"config.json", "config.yaml", "config.yml", "settings.json", "appsettings.json"}
	for _, configFile := range configFiles {
		if strings.Contains(fileName, configFile) {
			return true
		}
	}
	return false
}

func (es *EnvironmentScanner) isKubernetesFile(fileName string) bool {
	k8sFiles := []string{"kubernetes.yaml", "k8s.yaml", ".yaml", ".yml"}
	for _, k8sFile := range k8sFiles {
		if strings.Contains(fileName, k8sFile) && (strings.Contains(fileName, "k8s") || strings.Contains(fileName, "kubernetes")) {
			return true
		}
	}
	return false
}

func (es *EnvironmentScanner) isDebugEnabled(line string) bool {
	debugPatterns := []string{
		`(?i)"debug"\s*:\s*true`,
		`(?i)debug\s*[:=]\s*true`,
		`(?i)"development"\s*:\s*true`,
	}

	for _, pattern := range debugPatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (es *EnvironmentScanner) isInsecureSSL(line string) bool {
	insecurePatterns := []string{
		`(?i)"verify[_-]?ssl"\s*:\s*false`,
		`(?i)"ssl[_-]?verify"\s*:\s*false`,
		`(?i)"tls[_-]?verify"\s*:\s*false`,
		`(?i)"insecure"\s*:\s*true`,
	}

	for _, pattern := range insecurePatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (es *EnvironmentScanner) maskSecrets(line string) string {
	// Mask potential secrets in the line for display
	secretPattern := regexp.MustCompile(`([:=]\s*["']?)([^"'\s]{8,})(["']?)`)
	return secretPattern.ReplaceAllString(line, "${1}****${3}")
}
