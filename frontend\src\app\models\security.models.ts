export interface SecurityIssue {
  id: string;
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  title: string;
  description: string;
  file: string;
  line: number;
  column?: number;
  code: string;
  chain: string;
  category: string;
  cwe?: string;
  owasp?: string;
  references: string[];
  suggestion: string;
  metadata?: { [key: string]: string };
  created_at: string;
  updated_at: string;
}

export interface ScanResult {
  id: string;
  project_path: string;
  chains: string[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  start_time: string;
  end_time: string;
  duration: number;
  issues: SecurityIssue[];
  severity_counts: { [key: string]: number };
  files_scanned: number;
  lines_scanned: number;
  error?: string;
  configuration: { [key: string]: any };
  created_at: string;
  updated_at: string;
}

export interface ScanRequest {
  project_path: string;
  chains: string[];
  scan_type?: string;
  file_path?: string;
  options?: { [key: string]: any };
}

export interface ScanResponse {
  scan_id: string;
  status: string;
  message: string;
}

export interface SecurityChecklist {
  id: string;
  category: string;
  chain: string;
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'pending' | 'completed' | 'skipped' | 'failed';
  auto_check: boolean;
  references: string[];
  created_at: string;
  updated_at: string;
}

export interface SecurityReport {
  id: string;
  scan_result_id: string;
  scan_result: ScanResult;
  format: string;
  content: string;
  file_path: string;
  summary: { [key: string]: any };
  created_at: string;
  updated_at: string;
}

export interface HealthCheck {
  status: string;
  version: string;
  timestamp: string;
  services: { [key: string]: string };
}

export interface DashboardStats {
  total_scans: number;
  total_issues: number;
  critical_issues: number;
  high_issues: number;
  medium_issues: number;
  low_issues: number;
  recent_scans: ScanResult[];
  top_issues: SecurityIssue[];
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

export const SEVERITY_COLORS = {
  critical: '#f44336',
  high: '#ff9800',
  medium: '#ffeb3b',
  low: '#4caf50',
  info: '#2196f3'
};

export const CHAIN_ICONS = {
  ethereum: 'currency_bitcoin',
  bitcoin: 'currency_bitcoin',
  general: 'security'
};

export const CATEGORY_ICONS = {
  smart_contract: 'code',
  wallet: 'account_balance_wallet',
  environment: 'settings',
  dependency: 'extension'
};
