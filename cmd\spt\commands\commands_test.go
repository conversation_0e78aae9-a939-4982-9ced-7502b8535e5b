package commands

import (
	"bytes"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"

	"github.com/spf13/cobra"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCommands tests the CLI commands
func TestCommands(t *testing.T) {
	// Create temporary directory for tests
	tmpDir, err := os.MkdirTemp("", "spt_cli_test")
	require.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	// Change to temp directory
	oldDir, err := os.Getwd()
	require.NoError(t, err)
	defer os.Chdir(oldDir)

	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	t.Run("ScanCommand", func(t *testing.T) {
		testScanCommand(t, tmpDir)
	})

	t.Run("ConfigCommand", func(t *testing.T) {
		testConfigCommand(t, tmpDir)
	})

	t.Run("CheckCommand", func(t *testing.T) {
		testCheckCommand(t, tmpDir)
	})
}

func testScanCommand(t *testing.T, tmpDir string) {
	// Create test files
	testFiles := map[string]string{
		"package.json": `{
			"name": "test-app",
			"dependencies": {
				"lodash": "4.17.11"
			}
		}`,
		"test.sol": `pragma solidity ^0.8.0;
		contract Test {
			function vulnerable() public {
				// This is a test contract
			}
		}`,
	}

	for filename, content := range testFiles {
		err := os.WriteFile(filepath.Join(tmpDir, filename), []byte(content), 0644)
		require.NoError(t, err)
	}

	// Test scan command
	cmd := NewScanCommand()
	cmd.SetArgs([]string{"--format", "json", tmpDir})

	// Capture output
	var buf bytes.Buffer
	cmd.SetOut(&buf)
	cmd.SetErr(&buf)

	// Execute command
	err := cmd.Execute()

	// The command might fail due to missing dependencies, but it should not panic
	// and should provide meaningful output
	output := buf.String()

	// Check that the command at least tried to scan
	assert.Contains(t, output, "scan", "Output should mention scanning")
}

func testConfigCommand(t *testing.T, tmpDir string) {
	// Test config init
	cmd := NewConfigCommand()
	initCmd := cmd.Commands()[0] // config init

	configFile := filepath.Join(tmpDir, "test-config.yaml")
	initCmd.SetArgs([]string{"--output", configFile})

	var buf bytes.Buffer
	initCmd.SetOut(&buf)
	initCmd.SetErr(&buf)

	err := initCmd.Execute()
	require.NoError(t, err)

	// Check that config file was created
	assert.FileExists(t, configFile)

	// Check file content
	content, err := os.ReadFile(configFile)
	require.NoError(t, err)

	configStr := string(content)
	assert.Contains(t, configStr, "security:")
	assert.Contains(t, configStr, "scanning:")
	assert.Contains(t, configStr, "logging:")
}

func testCheckCommand(t *testing.T, tmpDir string) {
	// Create test dependency file
	packageJSON := `{
		"name": "test-app",
		"dependencies": {
			"lodash": "4.17.11"
		}
	}`

	err := os.WriteFile(filepath.Join(tmpDir, "package.json"), []byte(packageJSON), 0644)
	require.NoError(t, err)

	// Test check deps command
	cmd := NewCheckCommand()
	depsCmd := cmd.Commands()[0] // check deps

	depsCmd.SetArgs([]string{tmpDir})

	var buf bytes.Buffer
	depsCmd.SetOut(&buf)
	depsCmd.SetErr(&buf)

	err = depsCmd.Execute()

	// The command might fail due to missing dependencies, but should not panic
	output := buf.String()
	assert.Contains(t, output, "dependencies", "Output should mention dependencies")
}

// TestLoadConfig tests configuration loading
func TestLoadConfig(t *testing.T) {
	// Create a mock command
	cmd := &cobra.Command{}
	cmd.Flags().String("config", "", "Config file path")
	cmd.Flags().String("log-level", "", "Log level")
	cmd.Flags().Bool("verbose", false, "Verbose output")
	cmd.Flags().Bool("quiet", false, "Quiet mode")

	// Test loading default config
	cfg, err := loadConfig(cmd)
	require.NoError(t, err)
	assert.NotNil(t, cfg)
	assert.Equal(t, "development", cfg.Environment)
	assert.Equal(t, "medium", cfg.Security.Level)
}

// TestFilterIssuesBySeverity tests severity filtering
func TestFilterIssuesBySeverity(t *testing.T) {
	issues := []models.SecurityIssue{
		{Severity: "low", Title: "Low issue"},
		{Severity: "medium", Title: "Medium issue"},
		{Severity: "high", Title: "High issue"},
		{Severity: "critical", Title: "Critical issue"},
	}

	// Test filtering by high severity
	filtered := filterIssuesBySeverity(issues, "high")
	assert.Len(t, filtered, 2) // high and critical

	// Test filtering by medium severity
	filtered = filterIssuesBySeverity(issues, "medium")
	assert.Len(t, filtered, 3) // medium, high, and critical

	// Test no filtering
	filtered = filterIssuesBySeverity(issues, "")
	assert.Len(t, filtered, 4) // all issues
}

// TestDisplayResults tests result display functionality
func TestDisplayResults(t *testing.T) {
	issues := []models.SecurityIssue{
		{
			ID:          "TEST-001",
			Type:        "test_vulnerability",
			Severity:    "high",
			Title:       "Test Vulnerability",
			Description: "This is a test vulnerability",
			File:        "test.sol",
			Line:        42,
			Chain:       "ethereum",
			Category:    "smart_contract",
		},
	}

	opts := &ScanOptions{
		Format: "json",
	}

	// Test JSON display (should not error)
	err := displayResults(issues, opts)
	assert.NoError(t, err)

	// Test table display
	opts.Format = "table"
	err = displayResults(issues, opts)
	assert.NoError(t, err)

	// Test empty issues
	err = displayResults([]models.SecurityIssue{}, opts)
	assert.NoError(t, err)
}

// TestShouldExcludeFile tests file exclusion logic
func TestShouldExcludeFile(t *testing.T) {
	opts := &ScanOptions{
		Exclude: []string{"node_modules", "*.test.js"},
	}

	// Test exclusion
	assert.True(t, shouldExcludeFile("node_modules/package/file.js", opts))
	assert.True(t, shouldExcludeFile("test.test.js", opts))

	// Test inclusion
	assert.False(t, shouldExcludeFile("src/main.js", opts))
	assert.False(t, shouldExcludeFile("contract.sol", opts))
}

// TestShouldIncludeFile tests file inclusion logic
func TestShouldIncludeFile(t *testing.T) {
	opts := &ScanOptions{
		Include: []string{"*.sol", "*.js"},
	}

	// Test inclusion
	assert.True(t, shouldIncludeFile("contract.sol", opts))
	assert.True(t, shouldIncludeFile("script.js", opts))

	// Test exclusion
	assert.False(t, shouldIncludeFile("readme.md", opts))
	assert.False(t, shouldIncludeFile("config.yaml", opts))

	// Test empty include (should include all)
	opts.Include = []string{}
	assert.True(t, shouldIncludeFile("any-file.txt", opts))
}

// TestGetChainDisplay tests chain display formatting
func TestGetChainDisplay(t *testing.T) {
	assert.Equal(t, "all", getChainDisplay(""))
	assert.Equal(t, "ethereum", getChainDisplay("ethereum"))
	assert.Equal(t, "bitcoin", getChainDisplay("bitcoin"))
}

// TestGetSeverityEmoji tests severity emoji mapping
func TestGetSeverityEmoji(t *testing.T) {
	assert.Equal(t, "🔴", getSeverityEmoji("critical"))
	assert.Equal(t, "🟠", getSeverityEmoji("high"))
	assert.Equal(t, "🟡", getSeverityEmoji("medium"))
	assert.Equal(t, "🟢", getSeverityEmoji("low"))
	assert.Equal(t, "⚪", getSeverityEmoji("unknown"))
}

// TestTruncateString tests string truncation
func TestTruncateString(t *testing.T) {
	assert.Equal(t, "short", truncateString("short", 10))
	assert.Equal(t, "this is...", truncateString("this is a very long string", 10))
	assert.Equal(t, "exact", truncateString("exact", 5))
}

// Benchmark tests
func BenchmarkFilterIssuesBySeverity(b *testing.B) {
	issues := make([]models.SecurityIssue, 1000)
	for i := range issues {
		severities := []string{"low", "medium", "high", "critical"}
		issues[i] = models.SecurityIssue{
			Severity: severities[i%4],
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		filterIssuesBySeverity(issues, "high")
	}
}

func BenchmarkDisplayResults(b *testing.B) {
	issues := []models.SecurityIssue{
		{
			ID:          "BENCH-001",
			Type:        "benchmark_test",
			Severity:    "medium",
			Title:       "Benchmark Test",
			Description: "This is a benchmark test",
			File:        "bench.sol",
			Line:        1,
			Chain:       "ethereum",
			Category:    "smart_contract",
		},
	}

	opts := &ScanOptions{
		Format: "json",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		displayResults(issues, opts)
	}
}

// Helper function to create test configuration
func createTestConfig() *config.Config {
	return &config.Config{
		Environment: "test",
		Security: config.SecurityConfig{
			Level: "medium",
		},
		Logging: config.LoggingConfig{
			Level: "info",
		},
		Scanning: config.ScanningConfig{
			Timeout: 60,
		},
	}
}
