import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="reports-container">
      <h1>📊 Security Reports</h1>
      
      <mat-card>
        <mat-card-header>
          <mat-card-title>Report Generation</mat-card-title>
          <mat-card-subtitle>Generate comprehensive security reports</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>This feature will allow you to generate detailed security reports in various formats.</p>
          <div class="actions">
            <button mat-raised-button color="primary">
              <mat-icon>assessment</mat-icon>
              Generate Report
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .reports-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .actions {
      margin-top: 20px;
    }
  `]
})
export class ReportsComponent {}
