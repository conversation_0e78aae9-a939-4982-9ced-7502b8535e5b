import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, map } from 'rxjs';
import { 
  ScanResult, 
  ScanRequest, 
  ScanResponse, 
  SecurityIssue, 
  SecurityChecklist, 
  SecurityReport, 
  HealthCheck,
  DashboardStats
} from '../models/security.models';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private readonly baseUrl = 'http://localhost:8080/api/v1';
  private scanStatusSubject = new BehaviorSubject<string>('idle');
  public scanStatus$ = this.scanStatusSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Health check
  getHealth(): Observable<HealthCheck> {
    return this.http.get<HealthCheck>(`${this.baseUrl}/health`);
  }

  // Scanning endpoints
  startScan(request: ScanRequest): Observable<ScanResponse> {
    this.scanStatusSubject.next('starting');
    return this.http.post<ScanResponse>(`${this.baseUrl}/scan/start`, request);
  }

  getScanResult(scanId: string): Observable<ScanResult> {
    return this.http.get<ScanResult>(`${this.baseUrl}/scan/result/${scanId}`);
  }

  getScanHistory(): Observable<{ scans: ScanResult[], total: number }> {
    return this.http.get<{ scans: ScanResult[], total: number }>(`${this.baseUrl}/scan/history`);
  }

  scanFile(filePath: string): Observable<{ file: string, issues: SecurityIssue[], count: number }> {
    const params = new HttpParams().set('file', filePath);
    return this.http.get<{ file: string, issues: SecurityIssue[], count: number }>(`${this.baseUrl}/scan/file`, { params });
  }

  // Report endpoints
  generateReport(request: any): Observable<SecurityReport> {
    return this.http.post<SecurityReport>(`${this.baseUrl}/report/generate`, request);
  }

  // Checklist endpoints
  getSecurityChecklist(chain?: string): Observable<{ checklist: SecurityChecklist[], total: number }> {
    let params = new HttpParams();
    if (chain) {
      params = params.set('chain', chain);
    }
    return this.http.get<{ checklist: SecurityChecklist[], total: number }>(`${this.baseUrl}/checklist`, { params });
  }

  // Configuration endpoints
  getConfiguration(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/config`);
  }

  updateConfiguration(config: any): Observable<{ message: string }> {
    return this.http.put<{ message: string }>(`${this.baseUrl}/config`, config);
  }

  // Dashboard data (mock implementation)
  getDashboardStats(): Observable<DashboardStats> {
    // This would typically call a real endpoint
    return this.http.get<{ scans: ScanResult[], total: number }>(`${this.baseUrl}/scan/history`)
      .pipe(
        map(response => {
          // Transform the data to dashboard stats format
          const stats: DashboardStats = {
            total_scans: response.total,
            total_issues: response.scans.reduce((sum, scan) => sum + (scan.issues?.length || 0), 0),
            critical_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['critical'] || 0), 0),
            high_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['high'] || 0), 0),
            medium_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['medium'] || 0), 0),
            low_issues: response.scans.reduce((sum, scan) => sum + (scan.severity_counts?.['low'] || 0), 0),
            recent_scans: response.scans.slice(0, 5),
            top_issues: response.scans.flatMap(scan => scan.issues || []).slice(0, 10)
          };
          return stats;
        })
      );
  }

  // WebSocket connection for real-time updates
  connectWebSocket(): WebSocket {
    const token = localStorage.getItem('auth_token');
    const wsUrl = `ws://localhost:8080/ws${token ? '?token=' + token : ''}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connected');
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'scan_status') {
        this.scanStatusSubject.next(data.status);
      }
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return ws;
  }

  // Utility methods
  updateScanStatus(status: string): void {
    this.scanStatusSubject.next(status);
  }

  getCurrentScanStatus(): string {
    return this.scanStatusSubject.value;
  }

  // Mock data generators for development
  generateMockScanResult(): ScanResult {
    return {
      id: 'scan_' + Date.now(),
      project_path: '/path/to/project',
      chains: ['ethereum', 'bitcoin'],
      status: 'completed',
      start_time: new Date().toISOString(),
      end_time: new Date().toISOString(),
      duration: 30000,
      issues: this.generateMockIssues(),
      severity_counts: {
        critical: 2,
        high: 5,
        medium: 8,
        low: 12,
        info: 3
      },
      files_scanned: 45,
      lines_scanned: 2500,
      configuration: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  generateMockIssues(): SecurityIssue[] {
    return [
      {
        id: 'issue_1',
        type: 'reentrancy',
        severity: 'critical',
        title: 'Reentrancy Vulnerability',
        description: 'External call followed by state change may be vulnerable to reentrancy attacks',
        file: 'contracts/Token.sol',
        line: 45,
        code: 'recipient.call{value: amount}("");',
        chain: 'ethereum',
        category: 'smart_contract',
        cwe: 'CWE-362',
        owasp: 'A06:2021',
        references: ['https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/'],
        suggestion: 'Use the checks-effects-interactions pattern or reentrancy guards',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'issue_2',
        type: 'private_key_exposure',
        severity: 'critical',
        title: 'Private Key Exposure',
        description: 'Bitcoin private key found in code',
        file: 'src/wallet.js',
        line: 23,
        code: 'const privateKey = "L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ";',
        chain: 'bitcoin',
        category: 'wallet',
        cwe: 'CWE-798',
        owasp: 'A02:2021',
        references: ['https://bitcoin.org/en/secure-your-wallet'],
        suggestion: 'Remove private keys from code and use secure key management',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  generateMockChecklist(): SecurityChecklist[] {
    return [
      {
        id: 'check_1',
        category: 'wallet',
        chain: 'general',
        title: 'Private Key Security',
        description: 'Ensure private keys are not exposed in code',
        priority: 'critical',
        status: 'pending',
        auto_check: true,
        references: ['https://owasp.org/www-project-top-ten/'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'check_2',
        category: 'smart_contract',
        chain: 'ethereum',
        title: 'Reentrancy Protection',
        description: 'Check for reentrancy vulnerabilities in smart contracts',
        priority: 'high',
        status: 'completed',
        auto_check: true,
        references: ['https://consensys.github.io/smart-contract-best-practices/'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
