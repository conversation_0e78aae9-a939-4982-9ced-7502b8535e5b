import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AuthService, RegisterRequest } from '../../services/auth.service';

// Custom validator for password confirmation
function passwordMatchValidator(control: AbstractControl): {[key: string]: any} | null {
  const password = control.get('password');
  const confirmPassword = control.get('confirmPassword');
  
  if (password && confirmPassword && password.value !== confirmPassword.value) {
    return { 'passwordMismatch': true };
  }
  return null;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule
  ],
  template: `
    <div class="register-container">
      <div class="register-card-wrapper">
        <mat-card class="register-card">
          <mat-card-header class="register-header">
            <div class="logo">
              <mat-icon class="logo-icon">security</mat-icon>
              <h1>Create Account</h1>
            </div>
            <mat-card-subtitle>Join SPT Security Platform</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
              <div class="form-fields">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Username</mat-label>
                  <input matInput formControlName="username" autocomplete="username">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
                    Username is required
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('username')?.hasError('minlength')">
                    Username must be at least 3 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput formControlName="email" type="email" autocomplete="email">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                    Email is required
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                    Please enter a valid email
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Password</mat-label>
                  <input matInput 
                         [type]="hidePassword ? 'password' : 'text'" 
                         formControlName="password"
                         autocomplete="new-password">
                  <button mat-icon-button matSuffix 
                          (click)="hidePassword = !hidePassword" 
                          type="button">
                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                    Password is required
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                    Password must be at least 8 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Confirm Password</mat-label>
                  <input matInput 
                         [type]="hideConfirmPassword ? 'password' : 'text'" 
                         formControlName="confirmPassword"
                         autocomplete="new-password">
                  <button mat-icon-button matSuffix 
                          (click)="hideConfirmPassword = !hideConfirmPassword" 
                          type="button">
                    <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                    Please confirm your password
                  </mat-error>
                  <mat-error *ngIf="registerForm.hasError('passwordMismatch') && !registerForm.get('confirmPassword')?.hasError('required')">
                    Passwords do not match
                  </mat-error>
                </mat-form-field>

                <div class="terms-checkbox">
                  <mat-checkbox formControlName="acceptTerms">
                    I agree to the <a href="#" class="terms-link">Terms of Service</a> 
                    and <a href="#" class="terms-link">Privacy Policy</a>
                  </mat-checkbox>
                  <mat-error *ngIf="registerForm.get('acceptTerms')?.hasError('required') && registerForm.get('acceptTerms')?.touched">
                    You must accept the terms and conditions
                  </mat-error>
                </div>
              </div>

              <div class="form-actions">
                <button mat-raised-button 
                        color="primary" 
                        type="submit" 
                        class="register-button"
                        [disabled]="registerForm.invalid || isLoading">
                  <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
                  <span *ngIf="!isLoading">Create Account</span>
                </button>
              </div>
            </form>
          </mat-card-content>

          <mat-card-actions class="card-actions">
            <p>Already have an account? 
              <a routerLink="/login" class="login-link">Sign in</a>
            </p>
          </mat-card-actions>
        </mat-card>

        <div class="benefits-info">
          <h3>🚀 Why Choose SPT?</h3>
          <ul>
            <li>🔍 Advanced vulnerability detection</li>
            <li>📊 Real-time security monitoring</li>
            <li>📈 Comprehensive analytics</li>
            <li>🛡️ Enterprise-grade security</li>
            <li>🔧 Easy integration</li>
            <li>📱 Multi-platform support</li>
          </ul>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .register-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .register-card-wrapper {
      display: flex;
      gap: 40px;
      align-items: center;
      max-width: 1000px;
      width: 100%;
    }

    .register-card {
      flex: 1;
      max-width: 450px;
      padding: 20px;
    }

    .register-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin-bottom: 10px;
    }

    .logo-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #667eea;
    }

    .logo h1 {
      margin: 0;
      color: #333;
      font-weight: 300;
    }

    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 30px;
    }

    .full-width {
      width: 100%;
    }

    .terms-checkbox {
      margin-top: 10px;
    }

    .terms-link {
      color: #667eea;
      text-decoration: none;
    }

    .terms-link:hover {
      text-decoration: underline;
    }

    .form-actions {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .register-button {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }

    .card-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
    }

    .login-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .login-link:hover {
      text-decoration: underline;
    }

    .benefits-info {
      flex: 1;
      color: white;
      padding: 40px;
    }

    .benefits-info h3 {
      font-size: 24px;
      margin-bottom: 20px;
      font-weight: 300;
    }

    .benefits-info ul {
      list-style: none;
      padding: 0;
    }

    .benefits-info li {
      padding: 8px 0;
      font-size: 16px;
      opacity: 0.9;
    }

    @media (max-width: 768px) {
      .register-card-wrapper {
        flex-direction: column;
        gap: 20px;
      }

      .benefits-info {
        order: -1;
        padding: 20px;
        text-align: center;
      }

      .register-card {
        max-width: 100%;
      }
    }

    mat-spinner {
      margin-right: 10px;
    }

    mat-error {
      font-size: 12px;
      margin-top: 5px;
    }
  `]
})
export class RegisterComponent {
  registerForm: FormGroup;
  hidePassword = true;
  hideConfirmPassword = true;
  isLoading = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.registerForm = this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      acceptTerms: [false, [Validators.requiredTrue]]
    }, { validators: passwordMatchValidator });
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      
      const userData: RegisterRequest = {
        username: this.registerForm.value.username,
        email: this.registerForm.value.email,
        password: this.registerForm.value.password,
        confirmPassword: this.registerForm.value.confirmPassword
      };

      this.authService.register(userData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('Registration successful! Welcome to SPT!', 'Close', { duration: 3000 });
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });
        }
      });
    }
  }
}
