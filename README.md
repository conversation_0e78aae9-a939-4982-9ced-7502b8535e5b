# Blockchain Security Protocol Tool (SPT)

A comprehensive DevSecOps protocol tool for Ethereum and Bitcoin blockchain development security.

## 🎯 Overview

SPT is a security-focused tool designed to ensure end-to-end security and best practices for blockchain development. It provides real-time security monitoring, vulnerability detection, and automated security checks for Ethereum (EVM-based) and Bitcoin blockchain projects.

## 🚀 Features

### 🔐 Security Scanning
- **Wallet/Key Leak Detection**: Detect private keys, mnemonics, and secrets in code, .env files, and git history
- **Smart Contract Auditing**: Comprehensive Solidity vulnerability detection (re-entrancy, overflow, gas optimization)
- **Bitcoin Script Analysis**: UTXO model security, multisig validation, and wallet security patterns
- **Dependency Scanning**: Third-party package vulnerability detection with CVE feeds

### 🛡️ Development Security
- **Environment Security**: CI/CD pipeline security checks and development environment hardening
- **Deployment Safety**: Pre-deployment validation and secure deployment practices
- **Real-time Monitoring**: Live security suggestions and interactive developer checklists

### 🧰 Tools & Integrations
- **CLI Interface**: Comprehensive command-line tools for security operations
- **Web Dashboard**: Angular+Material frontend for security monitoring and reporting
- **VS Code Extension**: Real-time security highlighting and inline suggestions
- **Report Generation**: Markdown/PDF security reports with timestamped audits

## 🏗️ Architecture

```
SPT/
├── backend/           # Go backend services
├── frontend/          # Angular+Material web dashboard
├── cli/              # Command-line interface
├── vscode-extension/ # VS Code extension
├── plugins/          # Blockchain-specific security modules
├── security/         # Security templates and configurations
└── docs/             # Documentation and guides
```

## 🛠️ Technology Stack

- **Backend**: Go (Gin framework, security scanning engines)
- **Frontend**: Angular + Angular Material
- **CLI**: Go (Cobra CLI framework)
- **VS Code Extension**: TypeScript
- **Database**: SQLite/PostgreSQL for audit logs
- **Security**: Integration with Slither, OSV, CVE databases

## 📋 Quick Start

### Prerequisites
- Go 1.21+
- Node.js 18+
- Angular CLI
- VS Code (for extension development)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd Blockchain.SPT

# Install backend dependencies
cd backend && go mod tidy

# Install frontend dependencies
cd ../frontend && npm install

# Build CLI tool
cd ../cli && go build -o spt

# Install VS Code extension
cd ../vscode-extension && npm install && npm run compile
```

### Usage
```bash
# Scan Ethereum project
./spt scan --chain ethereum

# Scan Bitcoin project
./spt scan --chain bitcoin

# Audit smart contracts
./spt audit contracts/

# Check for key leaks
./spt check-keys

# Secure environment
./spt secure-env

# Generate security report
./spt report --output summary.md
```

## 🔧 Configuration

Create `spt.config.json` in your project root:
```json
{
  "chains": ["ethereum", "bitcoin"],
  "scanPaths": ["./contracts", "./src"],
  "excludePaths": ["./node_modules", "./build"],
  "securityLevel": "strict",
  "reportFormat": "markdown"
}
```

## 📚 Documentation

- [Getting Started Guide](docs/getting-started.md)
- [Security Best Practices](docs/security-practices.md)
- [API Documentation](docs/api.md)
- [Plugin Development](docs/plugin-development.md)

## 🤝 Contributing

Please read our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- [Documentation](docs/)
- [Issue Tracker](https://github.com/your-org/blockchain-spt/issues)
- [Discussions](https://github.com/your-org/blockchain-spt/discussions)
