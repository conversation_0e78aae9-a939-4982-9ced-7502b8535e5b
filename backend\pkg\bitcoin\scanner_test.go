package bitcoin

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/spt/backend/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test Bitcoin code samples
const vulnerableBitcoinCode = `
// Bitcoin wallet implementation
const privateKey = "L1aW4aubDFB7yfras2S1mN3bqg9nkoLmJebSLD5BWv3ENZ";
const address = "**********************************";

// Hardcoded RPC credentials
const rpcUser = "bitcoinrpc";
const rpcPassword = "password123";

// UTXO handling
function spendUTXO(txid, vout) {
    // No confirmation check
    const utxo = getUTXO(txid, vout);
    return createTransaction(utxo);
}

// Multisig setup
const multisigScript = "OP_1 " + pubkey1 + " " + pubkey2 + " " + pubkey3 + " OP_3 OP_CHECKMULTISIG";

// Seed phrase
const seedPhrase = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
`

const secureBitcoinCode = `
// Secure Bitcoin wallet implementation
import { getPrivateKeyFromEnv } from './secure-config';
import { validateConfirmations } from './validation';

// Use environment variables for sensitive data
const privateKey = getPrivateKeyFromEnv();
const rpcCredentials = loadFromSecureConfig();

// Proper UTXO handling with confirmation checks
function spendUTXO(txid, vout, requiredConfirmations = 6) {
    const utxo = getUTXO(txid, vout);
    
    if (!validateConfirmations(utxo, requiredConfirmations)) {
        throw new Error('Insufficient confirmations');
    }
    
    return createTransaction(utxo);
}

// Secure multisig with proper validation
function createMultisigScript(pubkeys, requiredSigs) {
    if (requiredSigs < 2 || requiredSigs > pubkeys.length) {
        throw new Error('Invalid multisig configuration');
    }
    
    return buildMultisigScript(pubkeys, requiredSigs);
}

// HD wallet with proper derivation
const hdWallet = new HDWallet(masterSeed);
const address = hdWallet.deriveAddress("m/44'/0'/0'/0/0");
`

const scriptAnalysisCode = `
// Bitcoin script examples
const p2pkhScript = "OP_DUP OP_HASH160 " + pubkeyHash + " OP_EQUALVERIFY OP_CHECKSIG";
const p2shScript = "OP_HASH160 " + scriptHash + " OP_EQUAL";

// Dangerous script with deprecated opcodes
const dangerousScript = "OP_CAT OP_MUL OP_2DIV";

// Non-standard script
const nonStandardScript = "OP_RETURN " + data;

// Multisig scripts
const validMultisig = "OP_2 " + pubkey1 + " " + pubkey2 + " " + pubkey3 + " OP_3 OP_CHECKMULTISIG";
const weakMultisig = "OP_1 " + pubkey1 + " " + pubkey2 + " OP_2 OP_CHECKMULTISIG"; // 1-of-2
`

func TestScanner_ScanFile(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
			Rules: config.SecurityRulesConfig{
				PrivateKeyExposure: config.KeyExposureConfig{
					Enabled: true,
					Bitcoin: config.BitcoinConfig{
						PrivateKeys: true,
						Addresses:   true,
						Seeds:       true,
						RPC:         true,
					},
				},
			},
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	tests := []struct {
		name           string
		code           string
		expectedIssues []string
		minIssues      int
	}{
		{
			name: "Vulnerable Bitcoin Code",
			code: vulnerableBitcoinCode,
			expectedIssues: []string{
				"private_key_exposure",
				"hardcoded_password",
				"seed_exposure",
			},
			minIssues: 3,
		},
		{
			name:      "Secure Bitcoin Code",
			code:      secureBitcoinCode,
			minIssues: 0, // Should have minimal issues
		},
		{
			name: "Script Analysis Code",
			code: scriptAnalysisCode,
			expectedIssues: []string{
				"insecure_script",
				"weak_multisig",
			},
			minIssues: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tmpFile, err := createTempBitcoinFile(tt.code)
			require.NoError(t, err)
			defer os.Remove(tmpFile)

			// Scan the file
			issues, err := scanner.ScanFile(context.Background(), tmpFile)
			require.NoError(t, err)

			// Check minimum number of issues
			assert.GreaterOrEqual(t, len(issues), tt.minIssues, "Expected at least %d issues, got %d", tt.minIssues, len(issues))

			// Check for expected issue types
			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expectedIssues {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}

			// Verify issue structure
			for _, issue := range issues {
				assert.NotEmpty(t, issue.ID)
				assert.NotEmpty(t, issue.Type)
				assert.NotEmpty(t, issue.Severity)
				assert.NotEmpty(t, issue.Title)
				assert.NotEmpty(t, issue.Description)
				assert.Equal(t, tmpFile, issue.File)
				assert.Equal(t, "bitcoin", issue.Chain)
				assert.Greater(t, issue.Line, 0)
			}
		})
	}
}

func TestScriptAnalyzer_AnalyzeScript(t *testing.T) {
	analyzer := NewScriptAnalyzer()

	tests := []struct {
		name     string
		code     string
		expected []string
	}{
		{
			name:     "Dangerous Opcodes",
			code:     `const script = "OP_CAT OP_MUL OP_2DIV";`,
			expected: []string{"insecure_script"},
		},
		{
			name:     "Weak Multisig",
			code:     `const multisig = "OP_1 pubkey1 pubkey2 OP_2 OP_CHECKMULTISIG";`,
			expected: []string{"weak_multisig"},
		},
		{
			name:     "Non-standard Script",
			code:     `const script = "OP_UNKNOWN_OPCODE data";`,
			expected: []string{"non_standard_script"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := analyzer.AnalyzeScript("test.js", tt.code)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestUTXOAnalyzer_AnalyzeUTXOs(t *testing.T) {
	analyzer := NewUTXOAnalyzer()

	tests := []struct {
		name     string
		code     string
		expected []string
	}{
		{
			name:     "Dust Outputs",
			code:     `const utxo = { value: 500, txid: "abc123" };`, // Below dust threshold
			expected: []string{"dust_outputs"},
		},
		{
			name:     "High Fee Transaction",
			code:     `const tx = { fee: 50000, size: 250 };`, // 200 sat/byte
			expected: []string{"high_transaction_fee"},
		},
		{
			name:     "Hardcoded Transaction ID",
			code:     `const txid = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";`,
			expected: []string{"hardcoded_txid"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := analyzer.AnalyzeUTXOs("test.js", tt.code)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestWalletAnalyzer_AnalyzeWallet(t *testing.T) {
	analyzer := NewWalletAnalyzer()

	tests := []struct {
		name     string
		code     string
		expected []string
	}{
		{
			name:     "Private Key Exposure",
			code:     `const privateKey = "L1aW4aubDFB7yfras2S1mN3bqg9nkoLmJebSLD5BWv3ENZ";`,
			expected: []string{"private_key_exposure"},
		},
		{
			name:     "Seed Exposure",
			code:     `const seed = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";`,
			expected: []string{"mnemonic_exposure"},
		},
		{
			name:     "Hardcoded Password",
			code:     `const walletPassword = "mypassword123";`,
			expected: []string{"hardcoded_password"},
		},
		{
			name:     "Testnet Address",
			code:     `const address = "mzBc4XEFSdzCDcTxAgf6EZXgsZWpztRhef";`,
			expected: []string{"testnet_address"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := analyzer.AnalyzeWallet("test.js", tt.code)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestScanner_ScanProject(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
		Scanning: config.ScanningConfig{
			Paths: config.PathsConfig{
				Include: []string{"*.js", "*.py", "*.go"},
				Exclude: []string{"node_modules", "test"},
			},
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	// Create temporary directory with Bitcoin code
	tmpDir, err := os.MkdirTemp("", "bitcoin_test")
	require.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	// Create test files
	files := map[string]string{
		"wallet.js":  vulnerableBitcoinCode,
		"secure.js":  secureBitcoinCode,
		"scripts.js": scriptAnalysisCode,
	}

	for filename, content := range files {
		filePath := filepath.Join(tmpDir, filename)
		err := os.WriteFile(filePath, []byte(content), 0644)
		require.NoError(t, err)
	}

	// Scan the project
	issues, err := scanner.ScanProject(context.Background(), tmpDir)
	require.NoError(t, err)

	// Should find issues across multiple files
	assert.Greater(t, len(issues), 0, "Should find security issues in the project")

	// Check that issues are from different files
	files_found := make(map[string]bool)
	for _, issue := range issues {
		files_found[filepath.Base(issue.File)] = true
	}

	// Should have issues from vulnerable files
	assert.True(t, files_found["wallet.js"], "Should find issues in wallet.js")
}

// Helper functions

func createTempBitcoinFile(content string) (string, error) {
	tmpFile, err := os.CreateTemp("", "test_*.js")
	if err != nil {
		return "", err
	}

	_, err = tmpFile.WriteString(content)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return "", err
	}

	tmpFile.Close()
	return tmpFile.Name(), nil
}

// Benchmark tests

func BenchmarkScanner_ScanFile(b *testing.B) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(b, err)

	tmpFile, err := createTempBitcoinFile(vulnerableBitcoinCode)
	require.NoError(b, err)
	defer os.Remove(tmpFile)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := scanner.ScanFile(context.Background(), tmpFile)
		require.NoError(b, err)
	}
}

func BenchmarkScriptAnalyzer_AnalyzeScript(b *testing.B) {
	analyzer := NewScriptAnalyzer()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := analyzer.AnalyzeScript("test.js", scriptAnalysisCode)
		require.NoError(b, err)
	}
}

func BenchmarkWalletAnalyzer_AnalyzeWallet(b *testing.B) {
	analyzer := NewWalletAnalyzer()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := analyzer.AnalyzeWallet("test.js", vulnerableBitcoinCode)
		require.NoError(b, err)
	}
}
