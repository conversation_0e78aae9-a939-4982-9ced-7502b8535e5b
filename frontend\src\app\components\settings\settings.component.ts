import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="settings-container">
      <h1>⚙️ Settings</h1>
      
      <mat-card>
        <mat-card-header>
          <mat-card-title>Configuration</mat-card-title>
          <mat-card-subtitle>Manage SPT settings and preferences</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>This feature will allow you to configure SPT settings, scan preferences, and security rules.</p>
          <div class="actions">
            <button mat-raised-button color="primary">
              <mat-icon>settings</mat-icon>
              Configure Settings
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .settings-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .actions {
      margin-top: 20px;
    }
  `]
})
export class SettingsComponent {}
