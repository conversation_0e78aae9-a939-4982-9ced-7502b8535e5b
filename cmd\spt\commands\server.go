package commands

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
)

// ServerOptions represents server command options
type ServerOptions struct {
	Port     int
	Host     string
	TLS      bool
	CertFile string
	KeyFile  string
	CORS     bool
	Auth     bool
	Dev      bool
}

// NewServerCommand creates the server command
func NewServerCommand() *cobra.Command {
	opts := &ServerOptions{}

	cmd := &cobra.Command{
		Use:   "server",
		Short: "Start SPT web server",
		Long: `Start the SPT web server for browser-based security analysis.

The server provides:
- Web-based security scanning interface
- Real-time scan results and reports
- Project management and history
- API endpoints for integration
- Dashboard with metrics and visualizations

Examples:
  spt server                                # Start server on default port
  spt server --port 8080                   # Start on specific port
  spt server --tls --cert cert.pem --key key.pem # Start with TLS
  spt server --dev                         # Development mode`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runServer(cmd, opts)
		},
	}

	// Add flags
	cmd.Flags().IntVarP(&opts.Port, "port", "p", 3000, "Server port")
	cmd.Flags().StringVar(&opts.Host, "host", "localhost", "Server host")
	cmd.Flags().BoolVar(&opts.TLS, "tls", false, "Enable TLS/HTTPS")
	cmd.Flags().StringVar(&opts.CertFile, "cert", "", "TLS certificate file")
	cmd.Flags().StringVar(&opts.KeyFile, "key", "", "TLS private key file")
	cmd.Flags().BoolVar(&opts.CORS, "cors", true, "Enable CORS")
	cmd.Flags().BoolVar(&opts.Auth, "auth", false, "Enable authentication")
	cmd.Flags().BoolVar(&opts.Dev, "dev", false, "Development mode")

	return cmd
}

// runServer starts the web server
func runServer(cmd *cobra.Command, opts *ServerOptions) error {
	// Load configuration
	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	fmt.Printf("🚀 Starting SPT Web Server\n")
	fmt.Printf("═══════════════════════════\n\n")

	// Display server configuration
	fmt.Printf("📊 Server Configuration:\n")
	fmt.Printf("   - Host: %s\n", opts.Host)
	fmt.Printf("   - Port: %d\n", opts.Port)
	fmt.Printf("   - TLS: %v\n", opts.TLS)
	fmt.Printf("   - CORS: %v\n", opts.CORS)
	fmt.Printf("   - Auth: %v\n", opts.Auth)
	fmt.Printf("   - Dev Mode: %v\n", opts.Dev)
	fmt.Printf("\n")

	// Validate TLS configuration
	if opts.TLS {
		if opts.CertFile == "" || opts.KeyFile == "" {
			return fmt.Errorf("TLS enabled but certificate or key file not specified")
		}
		if _, err := os.Stat(opts.CertFile); os.IsNotExist(err) {
			return fmt.Errorf("certificate file not found: %s", opts.CertFile)
		}
		if _, err := os.Stat(opts.KeyFile); os.IsNotExist(err) {
			return fmt.Errorf("key file not found: %s", opts.KeyFile)
		}
	}

	// Create HTTP server
	server := createHTTPServer(opts, cfg)

	// Start server in goroutine
	go func() {
		addr := fmt.Sprintf("%s:%d", opts.Host, opts.Port)
		
		if opts.TLS {
			fmt.Printf("🔒 Starting HTTPS server on https://%s\n", addr)
			if err := server.ListenAndServeTLS(opts.CertFile, opts.KeyFile); err != nil && err != http.ErrServerClosed {
				fmt.Printf("❌ HTTPS server failed: %v\n", err)
			}
		} else {
			fmt.Printf("🌐 Starting HTTP server on http://%s\n", addr)
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				fmt.Printf("❌ HTTP server failed: %v\n", err)
			}
		}
	}()

	// Display startup information
	protocol := "http"
	if opts.TLS {
		protocol = "https"
	}
	
	fmt.Printf("✅ Server started successfully!\n")
	fmt.Printf("🌐 Web Interface: %s://%s:%d\n", protocol, opts.Host, opts.Port)
	fmt.Printf("📡 API Endpoint: %s://%s:%d/api\n", protocol, opts.Host, opts.Port)
	fmt.Printf("📊 Health Check: %s://%s:%d/health\n", protocol, opts.Host, opts.Port)
	fmt.Printf("\n")
	fmt.Printf("💡 Available endpoints:\n")
	fmt.Printf("   • /              - Web dashboard\n")
	fmt.Printf("   • /api/scan      - Scan API\n")
	fmt.Printf("   • /api/reports   - Reports API\n")
	fmt.Printf("   • /api/projects  - Projects API\n")
	fmt.Printf("   • /health        - Health check\n")
	fmt.Printf("\n")
	fmt.Printf("🛑 Press Ctrl+C to stop the server\n")

	// Wait for interrupt signal
	return waitForShutdown(server)
}

// createHTTPServer creates and configures the HTTP server
func createHTTPServer(opts *ServerOptions, cfg interface{}) *http.Server {
	mux := http.NewServeMux()

	// Add routes
	addRoutes(mux, opts)

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", opts.Host, opts.Port),
		Handler:      mux,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return server
}

// addRoutes adds HTTP routes to the server
func addRoutes(mux *http.ServeMux, opts *ServerOptions) {
	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status":"healthy","timestamp":"%s"}`, time.Now().Format(time.RFC3339))
	})

	// Main dashboard
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}
		serveDashboard(w, r, opts)
	})

	// API endpoints
	mux.HandleFunc("/api/scan", handleScanAPI)
	mux.HandleFunc("/api/reports", handleReportsAPI)
	mux.HandleFunc("/api/projects", handleProjectsAPI)
	mux.HandleFunc("/api/status", handleStatusAPI)

	// Static files (if in development mode)
	if opts.Dev {
		mux.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("./web/static/"))))
	}
}

// serveDashboard serves the main dashboard
func serveDashboard(w http.ResponseWriter, r *http.Request, opts *ServerOptions) {
	w.Header().Set("Content-Type", "text/html")
	
	html := `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPT - Security Penetration Testing</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .feature { background: #f8f9fa; padding: 20px; border-radius: 6px; }
        .feature h3 { margin: 0 0 10px 0; color: #333; }
        .api-docs { background: #e3f2fd; padding: 20px; border-radius: 6px; margin-top: 20px; }
        .endpoint { margin: 10px 0; font-family: monospace; background: white; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 SPT - Security Penetration Testing</h1>
            <p>Blockchain Security Analysis Platform</p>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔍 Smart Contract Analysis</h3>
                <p>Comprehensive security analysis for Ethereum and Bitcoin smart contracts with vulnerability detection and gas optimization.</p>
            </div>
            <div class="feature">
                <h3>📦 Dependency Security</h3>
                <p>Scan project dependencies for known vulnerabilities across multiple package managers and languages.</p>
            </div>
            <div class="feature">
                <h3>🌍 Environment Security</h3>
                <p>Analyze environment configurations, Docker files, and CI/CD pipelines for security misconfigurations.</p>
            </div>
            <div class="feature">
                <h3>📊 Detailed Reports</h3>
                <p>Generate comprehensive security reports with actionable recommendations and compliance tracking.</p>
            </div>
        </div>

        <div class="api-docs">
            <h3>🔌 API Endpoints</h3>
            <p>Use these endpoints to integrate SPT with your development workflow:</p>
            <div class="endpoint">POST /api/scan - Start a security scan</div>
            <div class="endpoint">GET /api/reports - List available reports</div>
            <div class="endpoint">GET /api/projects - Manage projects</div>
            <div class="endpoint">GET /api/status - Check scan status</div>
            <div class="endpoint">GET /health - Health check</div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>SPT v1.0.0 - Blockchain Security Made Simple</p>
        </div>
    </div>
</body>
</html>`

	fmt.Fprint(w, html)
}

// API handlers
func handleScanAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	switch r.Method {
	case "POST":
		// Start a new scan
		w.WriteHeader(http.StatusAccepted)
		fmt.Fprintf(w, `{"message":"Scan started","scan_id":"scan_123","status":"running"}`)
	case "GET":
		// Get scan status
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"scans":[{"id":"scan_123","status":"completed","issues":5}]}`)
	default:
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprintf(w, `{"error":"Method not allowed"}`)
	}
}

func handleReportsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"reports":[{"id":"report_1","title":"Security Report","created":"2023-12-15T10:00:00Z"}]}`)
}

func handleProjectsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"projects":[{"id":"project_1","name":"My DApp","last_scan":"2023-12-15T10:00:00Z"}]}`)
}

func handleStatusAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status":"ready","version":"1.0.0","uptime":"1h30m","active_scans":0}`)
}

// waitForShutdown waits for interrupt signal and gracefully shuts down the server
func waitForShutdown(server *http.Server) error {
	// Create channel to receive OS signals
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Wait for signal
	<-quit
	fmt.Printf("\n🛑 Shutting down server...\n")

	// Create context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		fmt.Printf("❌ Server forced to shutdown: %v\n", err)
		return err
	}

	fmt.Printf("✅ Server stopped gracefully\n")
	return nil
}
