import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ApiService } from '../../services/api.service';
import { SecurityChecklist } from '../../models/security.models';

@Component({
  selector: 'app-checklist',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatCheckboxModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule
  ],
  template: `
    <div class="checklist-container">
      <h1>✅ Security Checklist</h1>
      
      <mat-card class="filter-card">
        <mat-card-content>
          <div class="filters">
            <mat-form-field appearance="outline">
              <mat-label>Filter by Chain</mat-label>
              <mat-select [(value)]="selectedChain" (selectionChange)="filterChecklist()">
                <mat-option value="">All Chains</mat-option>
                <mat-option value="ethereum">Ethereum</mat-option>
                <mat-option value="bitcoin">Bitcoin</mat-option>
                <mat-option value="general">General</mat-option>
              </mat-select>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Filter by Category</mat-label>
              <mat-select [(value)]="selectedCategory" (selectionChange)="filterChecklist()">
                <mat-option value="">All Categories</mat-option>
                <mat-option value="wallet">Wallet</mat-option>
                <mat-option value="smart_contract">Smart Contract</mat-option>
                <mat-option value="environment">Environment</mat-option>
                <mat-option value="deployment">Deployment</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Progress Overview -->
      <mat-card class="progress-card">
        <mat-card-header>
          <mat-card-title>Progress Overview</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="progress-stats">
            <div class="stat-item">
              <div class="stat-number">{{ getCompletedCount() }}</div>
              <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ getPendingCount() }}</div>
              <div class="stat-label">Pending</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ getProgressPercentage() }}%</div>
              <div class="stat-label">Progress</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Checklist Items -->
      <div class="checklist-sections">
        <div *ngFor="let category of getCategories()" class="category-section">
          <h2>{{ getCategoryTitle(category) }}</h2>
          
          <div class="checklist-items">
            <mat-card *ngFor="let item of getItemsByCategory(category)" 
                      class="checklist-item" 
                      [class.completed]="item.status === 'completed'"
                      [class.critical]="item.priority === 'critical'"
                      [class.high]="item.priority === 'high'">
              <mat-card-content>
                <div class="item-header">
                  <mat-checkbox 
                    [checked]="item.status === 'completed'"
                    (change)="toggleItem(item)"
                    [disabled]="item.auto_check">
                  </mat-checkbox>
                  <div class="item-info">
                    <h3>{{ item.title }}</h3>
                    <p>{{ item.description }}</p>
                  </div>
                  <div class="item-meta">
                    <mat-chip [class]="'priority-' + item.priority">{{ item.priority }}</mat-chip>
                    <mat-chip>{{ item.chain }}</mat-chip>
                  </div>
                </div>
                
                <div class="item-details" *ngIf="item.references?.length">
                  <div class="references">
                    <strong>References:</strong>
                    <ul>
                      <li *ngFor="let ref of item.references">
                        <a [href]="ref" target="_blank">{{ ref }}</a>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div class="item-actions">
                  <button *ngIf="item.auto_check" 
                          mat-stroked-button 
                          color="primary" 
                          (click)="runAutoCheck(item)">
                    <mat-icon>play_arrow</mat-icon>
                    Auto Check
                  </button>
                  <button mat-button (click)="markAsSkipped(item)">
                    <mat-icon>skip_next</mat-icon>
                    Skip
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <mat-card class="actions-card">
        <mat-card-content>
          <div class="actions">
            <button mat-raised-button color="primary" (click)="runAllAutoChecks()">
              <mat-icon>play_arrow</mat-icon>
              Run All Auto Checks
            </button>
            <button mat-raised-button (click)="exportChecklist()">
              <mat-icon>download</mat-icon>
              Export Checklist
            </button>
            <button mat-raised-button (click)="resetChecklist()">
              <mat-icon>refresh</mat-icon>
              Reset All
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .checklist-container {
      padding: 20px;
      max-width: 1000px;
      margin: 0 auto;
    }

    h1 {
      margin-bottom: 30px;
      color: #333;
    }

    .filter-card,
    .progress-card,
    .actions-card {
      margin-bottom: 30px;
    }

    .filters {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
    }

    .filters mat-form-field {
      min-width: 200px;
    }

    .progress-stats {
      display: flex;
      justify-content: space-around;
      text-align: center;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .stat-number {
      font-size: 36px;
      font-weight: bold;
      color: #2196f3;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
      text-transform: uppercase;
    }

    .category-section {
      margin-bottom: 40px;
    }

    .category-section h2 {
      margin-bottom: 20px;
      color: #333;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 10px;
    }

    .checklist-items {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .checklist-item {
      transition: all 0.3s ease;
    }

    .checklist-item.completed {
      background-color: #f1f8e9;
      border-left: 4px solid #4caf50;
    }

    .checklist-item.critical {
      border-left: 4px solid #f44336;
    }

    .checklist-item.high {
      border-left: 4px solid #ff9800;
    }

    .item-header {
      display: flex;
      align-items: flex-start;
      gap: 15px;
    }

    .item-info {
      flex: 1;
    }

    .item-info h3 {
      margin: 0 0 10px 0;
      color: #333;
    }

    .item-info p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }

    .item-meta {
      display: flex;
      flex-direction: column;
      gap: 5px;
      align-items: flex-end;
    }

    .priority-critical {
      background-color: #f44336;
      color: white;
    }

    .priority-high {
      background-color: #ff9800;
      color: white;
    }

    .priority-medium {
      background-color: #ffeb3b;
      color: black;
    }

    .priority-low {
      background-color: #4caf50;
      color: white;
    }

    .item-details {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
    }

    .references ul {
      margin: 10px 0;
      padding-left: 20px;
    }

    .references a {
      color: #1976d2;
      text-decoration: none;
    }

    .references a:hover {
      text-decoration: underline;
    }

    .item-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      justify-content: flex-end;
    }

    .actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .actions button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class ChecklistComponent implements OnInit {
  checklist: SecurityChecklist[] = [];
  filteredChecklist: SecurityChecklist[] = [];
  selectedChain = '';
  selectedCategory = '';

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadChecklist();
  }

  loadChecklist(): void {
    this.apiService.getSecurityChecklist().subscribe({
      next: (response) => {
        this.checklist = response.checklist;
        this.filteredChecklist = [...this.checklist];
      },
      error: (error) => {
        console.error('Error loading checklist:', error);
        // Use mock data for development
        this.checklist = this.apiService.generateMockChecklist();
        this.filteredChecklist = [...this.checklist];
      }
    });
  }

  filterChecklist(): void {
    this.filteredChecklist = this.checklist.filter(item => {
      const chainMatch = !this.selectedChain || item.chain === this.selectedChain;
      const categoryMatch = !this.selectedCategory || item.category === this.selectedCategory;
      return chainMatch && categoryMatch;
    });
  }

  getCategories(): string[] {
    const categories = [...new Set(this.filteredChecklist.map(item => item.category))];
    return categories.sort();
  }

  getCategoryTitle(category: string): string {
    return category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  getItemsByCategory(category: string): SecurityChecklist[] {
    return this.filteredChecklist.filter(item => item.category === category);
  }

  getCompletedCount(): number {
    return this.filteredChecklist.filter(item => item.status === 'completed').length;
  }

  getPendingCount(): number {
    return this.filteredChecklist.filter(item => item.status === 'pending').length;
  }

  getProgressPercentage(): number {
    if (this.filteredChecklist.length === 0) return 0;
    return Math.round((this.getCompletedCount() / this.filteredChecklist.length) * 100);
  }

  toggleItem(item: SecurityChecklist): void {
    item.status = item.status === 'completed' ? 'pending' : 'completed';
    item.updated_at = new Date().toISOString();
  }

  markAsSkipped(item: SecurityChecklist): void {
    item.status = 'skipped';
    item.updated_at = new Date().toISOString();
  }

  runAutoCheck(item: SecurityChecklist): void {
    // Simulate auto check
    console.log('Running auto check for:', item.title);
    
    // In a real implementation, this would call the backend
    setTimeout(() => {
      item.status = Math.random() > 0.3 ? 'completed' : 'failed';
      item.updated_at = new Date().toISOString();
    }, 1000);
  }

  runAllAutoChecks(): void {
    const autoCheckItems = this.filteredChecklist.filter(item => item.auto_check);
    autoCheckItems.forEach(item => this.runAutoCheck(item));
  }

  exportChecklist(): void {
    const dataStr = JSON.stringify(this.filteredChecklist, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-checklist-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }

  resetChecklist(): void {
    this.checklist.forEach(item => {
      item.status = 'pending';
      item.updated_at = new Date().toISOString();
    });
    this.filterChecklist();
  }
}
