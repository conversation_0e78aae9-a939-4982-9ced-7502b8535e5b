package dependencies

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"
	"github.com/sirupsen/logrus"
)

// CICDScanner scans CI/CD configuration files for security issues
type CICDScanner struct {
	logger *logrus.Logger
}

// CICDPlatform represents different CI/CD platforms
type CICDPlatform int

const (
	PlatformUnknown CICDPlatform = iota
	PlatformGitHubActions
	PlatformGitLabCI
	PlatformTravisCI
	PlatformCircleCI
	PlatformJenkins
	**********************
	PlatformAWSCodeBuild
	PlatformBuildkite
	PlatformDroneCI
)

// NewCICDScanner creates a new CI/CD scanner
func NewCICDScanner() *CICDScanner {
	return &CICDScanner{
		logger: logrus.New(),
	}
}

// ScanCICD scans the entire project for CI/CD security issues
func (cs *CICDScanner) ScanCICD(projectPath string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// This would walk through the project and scan CI/CD files
	// For now, return empty slice
	return issues, nil
}

// ScanFile scans a specific CI/CD file for security issues
func (cs *CICDScanner) ScanFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	platform := cs.detectPlatform(filePath)

	switch platform {
	case PlatformGitHubActions:
		githubIssues := cs.scanGitHubActions(filePath, content)
		issues = append(issues, githubIssues...)
	case PlatformGitLabCI:
		gitlabIssues := cs.scanGitLabCI(filePath, content)
		issues = append(issues, gitlabIssues...)
	case PlatformTravisCI:
		travisIssues := cs.scanTravisCI(filePath, content)
		issues = append(issues, travisIssues...)
	case PlatformCircleCI:
		circleIssues := cs.scanCircleCI(filePath, content)
		issues = append(issues, circleIssues...)
	case PlatformJenkins:
		jenkinsIssues := cs.scanJenkins(filePath, content)
		issues = append(issues, jenkinsIssues...)
	default:
		// Generic CI/CD checks
		genericIssues := cs.scanGenericCICD(filePath, content)
		issues = append(issues, genericIssues...)
	}

	return issues, nil
}

// detectPlatform detects the CI/CD platform based on file path
func (cs *CICDScanner) detectPlatform(filePath string) CICDPlatform {
	fileName := strings.ToLower(filePath)

	if strings.Contains(fileName, ".github/workflows") {
		return PlatformGitHubActions
	}
	if strings.Contains(fileName, ".gitlab-ci.yml") {
		return PlatformGitLabCI
	}
	if strings.Contains(fileName, ".travis.yml") {
		return PlatformTravisCI
	}
	if strings.Contains(fileName, "circle") && strings.Contains(fileName, "config.yml") {
		return PlatformCircleCI
	}
	if strings.Contains(fileName, "jenkinsfile") {
		return PlatformJenkins
	}
	if strings.Contains(fileName, "azure-pipelines") {
		return **********************
	}
	if strings.Contains(fileName, "buildspec.yml") {
		return PlatformAWSCodeBuild
	}

	return PlatformUnknown
}

// scanGitHubActions scans GitHub Actions workflows
func (cs *CICDScanner) scanGitHubActions(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "github_actions")...)

		// Check for pull_request_target usage
		if strings.Contains(line, "pull_request_target") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("github_pr_target_%d", i+1),
				Type:        "dangerous_trigger",
				Severity:    "high",
				Title:       "Dangerous pull_request_target Trigger",
				Description: "pull_request_target can be dangerous as it runs with write permissions",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Use pull_request trigger instead or add proper security checks",
				References:  []string{"https://securitylab.github.com/research/github-actions-preventing-pwn-requests/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for script injection vulnerabilities
		if cs.hasScriptInjection(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("github_script_injection_%d", i+1),
				Type:        "script_injection",
				Severity:    "critical",
				Title:       "Script Injection Vulnerability",
				Description: "Potential script injection through unvalidated user input",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				CWE:         "CWE-94",
				Suggestion:  "Validate and sanitize user inputs, use environment variables",
				References:  []string{"https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for third-party actions without version pinning
		if cs.isUnpinnedAction(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("github_unpinned_action_%d", i+1),
				Type:        "unpinned_action",
				Severity:    "medium",
				Title:       "Unpinned Third-Party Action",
				Description: "Third-party action is not pinned to specific version",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Pin actions to specific commit SHA or version tag",
				References:  []string{"https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for excessive permissions
		if cs.hasExcessivePermissions(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("github_excessive_perms_%d", i+1),
				Type:        "excessive_permissions",
				Severity:    "medium",
				Title:       "Excessive Permissions",
				Description: "Workflow has excessive permissions granted",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Follow principle of least privilege",
				References:  []string{"https://docs.github.com/en/actions/security-guides/automatic-token-authentication"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanGitLabCI scans GitLab CI configuration
func (cs *CICDScanner) scanGitLabCI(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "gitlab_ci")...)

		// Check for script injection
		if cs.hasScriptInjection(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gitlab_script_injection_%d", i+1),
				Type:        "script_injection",
				Severity:    "critical",
				Title:       "Script Injection Vulnerability",
				Description: "Potential script injection in GitLab CI script",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Validate and sanitize inputs in CI scripts",
				References:  []string{"https://docs.gitlab.com/ee/ci/security/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for privileged mode
		if strings.Contains(line, "privileged: true") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gitlab_privileged_%d", i+1),
				Type:        "privileged_mode",
				Severity:    "high",
				Title:       "Privileged Mode Enabled",
				Description: "Docker container running in privileged mode",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Avoid privileged mode unless absolutely necessary",
				References:  []string{"https://docs.gitlab.com/ee/ci/docker/using_docker_build.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanTravisCI scans Travis CI configuration
func (cs *CICDScanner) scanTravisCI(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "travis_ci")...)

		// Check for sudo usage
		if strings.Contains(line, "sudo: required") || strings.Contains(line, "sudo: true") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("travis_sudo_%d", i+1),
				Type:        "sudo_required",
				Severity:    "medium",
				Title:       "Sudo Access Required",
				Description: "Build requires sudo access which increases attack surface",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Use container-based builds when possible",
				References:  []string{"https://docs.travis-ci.com/user/reference/overview/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanCircleCI scans CircleCI configuration
func (cs *CICDScanner) scanCircleCI(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "circle_ci")...)

		// Check for machine executor usage
		if strings.Contains(line, "machine: true") || strings.Contains(line, "executor: machine") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("circle_machine_%d", i+1),
				Type:        "machine_executor",
				Severity:    "low",
				Title:       "Machine Executor Usage",
				Description: "Using machine executor which has broader access",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Use Docker executor when possible for better isolation",
				References:  []string{"https://circleci.com/docs/2.0/executor-types/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanJenkins scans Jenkins pipeline files
func (cs *CICDScanner) scanJenkins(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "//") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "jenkins")...)

		// Check for script execution
		if strings.Contains(line, "sh ") && cs.hasScriptInjection(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("jenkins_script_injection_%d", i+1),
				Type:        "script_injection",
				Severity:    "high",
				Title:       "Script Injection in Jenkins",
				Description: "Potential script injection in Jenkins pipeline",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Validate inputs and use parameterized builds",
				References:  []string{"https://www.jenkins.io/doc/book/pipeline/security/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// scanGenericCICD scans generic CI/CD files
func (cs *CICDScanner) scanGenericCICD(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for hardcoded secrets
		issues = append(issues, cs.checkHardcodedSecrets(filePath, i+1, line, "generic")...)

		// Check for dangerous commands
		if cs.hasDangerousCommand(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("cicd_dangerous_cmd_%d", i+1),
				Type:        "dangerous_command",
				Severity:    "high",
				Title:       "Dangerous Command in CI/CD",
				Description: "Potentially dangerous command detected in CI/CD pipeline",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "cicd",
				Suggestion:  "Review and secure the command execution",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A1_2017-Injection"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkHardcodedSecrets checks for hardcoded secrets in CI/CD files
func (cs *CICDScanner) checkHardcodedSecrets(filePath string, lineNum int, line string, platform string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	secretPatterns := []struct {
		pattern     string
		description string
		severity    string
	}{
		{`(?i)(api[_-]?key|apikey)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded API key", "critical"},
		{`(?i)(secret[_-]?key|secretkey)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded secret key", "critical"},
		{`(?i)(access[_-]?token|accesstoken)\s*[:=]\s*["']?[^"'\s]{16,}["']?`, "Hardcoded access token", "critical"},
		{`(?i)(password|passwd|pwd)\s*[:=]\s*["']?[^"'\s]{8,}["']?`, "Hardcoded password", "critical"},
		{`(?i)(private[_-]?key|privatekey)\s*[:=]\s*["']?[^"'\s]{32,}["']?`, "Hardcoded private key", "critical"},
		{`(?i)(database[_-]?url|db[_-]?url)\s*[:=]\s*["']?[^"'\s]+["']?`, "Hardcoded database URL", "high"},
	}

	for _, pattern := range secretPatterns {
		if matched, _ := regexp.MatchString(pattern.pattern, line); matched {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("cicd_secret_%s_%d", platform, lineNum),
				Type:        "hardcoded_secret",
				Severity:    pattern.severity,
				Title:       "Hardcoded Secret in CI/CD",
				Description: pattern.description + " found in CI/CD configuration",
				File:        filePath,
				Line:        lineNum,
				Code:        cs.maskSecrets(line),
				Chain:       "general",
				Category:    "cicd",
				CWE:         "CWE-798",
				Suggestion:  "Use CI/CD platform's secret management features",
				References:  []string{"https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// hasScriptInjection checks for potential script injection
func (cs *CICDScanner) hasScriptInjection(line string) bool {
	injectionPatterns := []string{
		`\$\{\{.*\}\}`,           // GitHub Actions expression injection
		`\$[A-Z_]+`,              // Environment variable injection
		`\$\(.*\)`,               // Command substitution
		`\`.*\``,                 // Backtick command execution
		`eval\s*\(`,              // eval usage
		`exec\s*\(`,              // exec usage
	}

	for _, pattern := range injectionPatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}

	return false
}

// isUnpinnedAction checks if a GitHub Action is not pinned to a specific version
func (cs *CICDScanner) isUnpinnedAction(line string) bool {
	actionPattern := regexp.MustCompile(`uses:\s*([^@\s]+)@([^@\s]+)`)
	matches := actionPattern.FindStringSubmatch(line)

	if len(matches) > 2 {
		version := matches[2]
		// Check if it's not pinned to a commit SHA or specific version
		if version == "main" || version == "master" || version == "latest" {
			return true
		}
		// Check if it's a commit SHA (40 hex characters)
		if matched, _ := regexp.MatchString(`^[a-f0-9]{40}$`, version); !matched {
			// Not a commit SHA, could be a tag but still risky
			return true
		}
	}

	return false
}

// hasExcessivePermissions checks for excessive permissions in GitHub Actions
func (cs *CICDScanner) hasExcessivePermissions(line string) bool {
	excessivePatterns := []string{
		`permissions:\s*write-all`,
		`contents:\s*write`,
		`actions:\s*write`,
		`checks:\s*write`,
		`deployments:\s*write`,
		`issues:\s*write`,
		`packages:\s*write`,
		`pages:\s*write`,
		`pull-requests:\s*write`,
		`repository-projects:\s*write`,
		`security-events:\s*write`,
		`statuses:\s*write`,
	}

	for _, pattern := range excessivePatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}

	return false
}

// hasDangerousCommand checks for dangerous commands in CI/CD
func (cs *CICDScanner) hasDangerousCommand(line string) bool {
	dangerousPatterns := []string{
		`curl\s+.*\|\s*sh`,       // Piping curl to shell
		`wget\s+.*\|\s*sh`,       // Piping wget to shell
		`rm\s+-rf\s+/`,           // Dangerous file deletion
		`chmod\s+777`,            // Overly permissive permissions
		`sudo\s+`,                // Sudo usage
		`eval\s+`,                // eval usage
		`\$\(.*\)`,               // Command substitution with user input
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}

	return false
}

// maskSecrets masks secrets in the line for display
func (cs *CICDScanner) maskSecrets(line string) string {
	secretPattern := regexp.MustCompile(`([:=]\s*["']?)([^"'\s]{8,})(["']?)`)
	return secretPattern.ReplaceAllString(line, "${1}****${3}")
}
