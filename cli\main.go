package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/internal/scanner"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	cfgFile     string
	projectPath string
	chains      []string
	outputFile  string
	format      string
	verbose     bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "spt",
	Short: "Blockchain Security Protocol Tool",
	Long: `SPT is a comprehensive security tool for blockchain development.
It provides security scanning, vulnerability detection, and best practice
recommendations for Ethereum and Bitcoin blockchain projects.`,
}

// scanCmd represents the scan command
var scanCmd = &cobra.Command{
	Use:   "scan",
	Short: "Scan project for security vulnerabilities",
	Long: `Scan a blockchain project for security vulnerabilities and issues.
Supports Ethereum (Solidity) and Bitcoin projects.`,
	Run: func(cmd *cobra.Command, args []string) {
		runScan()
	},
}

// auditCmd represents the audit command
var auditCmd = &cobra.Command{
	Use:   "audit [path]",
	Short: "Audit smart contracts and scripts",
	Long: `Perform detailed security audit of smart contracts and blockchain scripts.
Provides comprehensive analysis of potential vulnerabilities.`,
	Args: cobra.MaximumNArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		auditPath := projectPath
		if len(args) > 0 {
			auditPath = args[0]
		}
		runAudit(auditPath)
	},
}

// checkKeysCmd represents the check-keys command
var checkKeysCmd = &cobra.Command{
	Use:   "check-keys",
	Short: "Check for exposed private keys and secrets",
	Long: `Scan the project for exposed private keys, mnemonics, and other secrets
that should not be present in the codebase.`,
	Run: func(cmd *cobra.Command, args []string) {
		runCheckKeys()
	},
}

// secureEnvCmd represents the secure-env command
var secureEnvCmd = &cobra.Command{
	Use:   "secure-env",
	Short: "Check environment security configuration",
	Long: `Analyze environment configuration files, CI/CD pipelines, and deployment
settings for security issues and best practices.`,
	Run: func(cmd *cobra.Command, args []string) {
		runSecureEnv()
	},
}

// reportCmd represents the report command
var reportCmd = &cobra.Command{
	Use:   "report",
	Short: "Generate security report",
	Long: `Generate comprehensive security reports in various formats (markdown, PDF, JSON)
based on previous scan results.`,
	Run: func(cmd *cobra.Command, args []string) {
		runReport()
	},
}

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("SPT (Blockchain Security Protocol Tool) v1.0.0")
		fmt.Println("Built for Ethereum and Bitcoin security scanning")
	},
}

func init() {
	cobra.OnInitialize(initConfig)

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./spt.config.json)")
	rootCmd.PersistentFlags().StringVarP(&projectPath, "path", "p", ".", "project path to scan")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")

	// Scan command flags
	scanCmd.Flags().StringSliceVarP(&chains, "chain", "c", []string{"ethereum", "bitcoin"}, "blockchain types to scan (ethereum, bitcoin)")
	scanCmd.Flags().StringVarP(&outputFile, "output", "o", "", "output file for results")
	scanCmd.Flags().StringVarP(&format, "format", "f", "json", "output format (json, markdown)")

	// Audit command flags
	auditCmd.Flags().StringSliceVarP(&chains, "chain", "c", []string{"ethereum", "bitcoin"}, "blockchain types to audit")
	auditCmd.Flags().StringVarP(&outputFile, "output", "o", "", "output file for audit results")

	// Report command flags
	reportCmd.Flags().StringVarP(&format, "format", "f", "markdown", "report format (markdown, pdf, json)")
	reportCmd.Flags().StringVarP(&outputFile, "output", "o", "security-report", "output file name")

	// Add commands to root
	rootCmd.AddCommand(scanCmd)
	rootCmd.AddCommand(auditCmd)
	rootCmd.AddCommand(checkKeysCmd)
	rootCmd.AddCommand(secureEnvCmd)
	rootCmd.AddCommand(reportCmd)
	rootCmd.AddCommand(versionCmd)
}

// initConfig reads in config file and ENV variables if set
func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.AddConfigPath(".")
		viper.SetConfigName("spt.config")
		viper.SetConfigType("json")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err == nil && verbose {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	}
}

// runScan executes the scan command
func runScan() {
	fmt.Printf("🔍 Starting security scan for project: %s\n", projectPath)
	fmt.Printf("📋 Scanning chains: %s\n", strings.Join(chains, ", "))

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize scanner
	scannerEngine, err := scanner.NewEngine(cfg)
	if err != nil {
		fmt.Printf("❌ Failed to initialize scanner: %v\n", err)
		os.Exit(1)
	}

	// Run scan
	ctx := context.Background()
	result, err := scannerEngine.ScanProject(ctx, projectPath, chains)
	if err != nil {
		fmt.Printf("❌ Scan failed: %v\n", err)
		os.Exit(1)
	}

	// Display results
	displayScanResults(result)

	// Save results if output file specified
	if outputFile != "" {
		if err := saveResults(result, outputFile, format); err != nil {
			fmt.Printf("❌ Failed to save results: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("💾 Results saved to: %s\n", outputFile)
	}
}

// runAudit executes the audit command
func runAudit(auditPath string) {
	fmt.Printf("🔍 Starting security audit for: %s\n", auditPath)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize scanner
	scannerEngine, err := scanner.NewEngine(cfg)
	if err != nil {
		fmt.Printf("❌ Failed to initialize scanner: %v\n", err)
		os.Exit(1)
	}

	// Check if it's a file or directory
	info, err := os.Stat(auditPath)
	if err != nil {
		fmt.Printf("❌ Path not found: %v\n", err)
		os.Exit(1)
	}

	ctx := context.Background()
	if info.IsDir() {
		// Audit entire directory
		result, err := scannerEngine.ScanProject(ctx, auditPath, chains)
		if err != nil {
			fmt.Printf("❌ Audit failed: %v\n", err)
			os.Exit(1)
		}
		displayScanResults(result)
	} else {
		// Audit single file
		issues, err := scannerEngine.ScanFile(ctx, auditPath)
		if err != nil {
			fmt.Printf("❌ File audit failed: %v\n", err)
			os.Exit(1)
		}
		displayFileResults(auditPath, issues)
	}
}

// runCheckKeys executes the check-keys command
func runCheckKeys() {
	fmt.Printf("🔑 Checking for exposed keys and secrets in: %s\n", projectPath)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize scanner
	scannerEngine, err := scanner.NewEngine(cfg)
	if err != nil {
		fmt.Printf("❌ Failed to initialize scanner: %v\n", err)
		os.Exit(1)
	}

	// Run key check scan
	ctx := context.Background()
	result, err := scannerEngine.ScanProject(ctx, projectPath, []string{"general"})
	if err != nil {
		fmt.Printf("❌ Key check failed: %v\n", err)
		os.Exit(1)
	}

	// Filter for key-related issues
	keyIssues := filterKeyIssues(result.Issues)

	if len(keyIssues) == 0 {
		fmt.Println("✅ No exposed keys or secrets found!")
	} else {
		fmt.Printf("⚠️  Found %d potential key/secret exposures:\n\n", len(keyIssues))
		for _, issue := range keyIssues {
			displayIssue(issue)
		}
	}
}

// runSecureEnv executes the secure-env command
func runSecureEnv() {
	fmt.Printf("🛡️  Checking environment security for: %s\n", projectPath)

	// Check for common security files and configurations
	securityChecks := []struct {
		file        string
		description string
		required    bool
	}{
		{".gitignore", "Git ignore file", true},
		{".env.example", "Environment example file", false},
		{"docker-compose.yml", "Docker compose configuration", false},
		{"Dockerfile", "Docker configuration", false},
		{".github/workflows", "GitHub Actions workflows", false},
	}

	fmt.Println("\n📋 Environment Security Checklist:")
	for _, check := range securityChecks {
		checkPath := filepath.Join(projectPath, check.file)
		if _, err := os.Stat(checkPath); err == nil {
			fmt.Printf("✅ %s - Found\n", check.description)
		} else if check.required {
			fmt.Printf("❌ %s - Missing (Required)\n", check.description)
		} else {
			fmt.Printf("⚪ %s - Not found (Optional)\n", check.description)
		}
	}

	fmt.Println("\n🔍 Running environment-specific security scan...")

	// Load configuration and run environment scan
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	scannerEngine, err := scanner.NewEngine(cfg)
	if err != nil {
		fmt.Printf("❌ Failed to initialize scanner: %v\n", err)
		os.Exit(1)
	}

	ctx := context.Background()
	result, err := scannerEngine.ScanProject(ctx, projectPath, []string{"general"})
	if err != nil {
		fmt.Printf("❌ Environment scan failed: %v\n", err)
		os.Exit(1)
	}

	// Filter for environment-related issues
	envIssues := filterEnvironmentIssues(result.Issues)

	if len(envIssues) == 0 {
		fmt.Println("✅ No environment security issues found!")
	} else {
		fmt.Printf("⚠️  Found %d environment security issues:\n\n", len(envIssues))
		for _, issue := range envIssues {
			displayIssue(issue)
		}
	}
}

// runReport executes the report command
func runReport() {
	fmt.Printf("📊 Generating security report in %s format\n", format)

	// For now, create a sample report
	reportContent := generateSampleReport()

	filename := fmt.Sprintf("%s.%s", outputFile, getFileExtension(format))

	if err := os.WriteFile(filename, []byte(reportContent), 0644); err != nil {
		fmt.Printf("❌ Failed to write report: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Report generated: %s\n", filename)
}

// Helper functions

func displayScanResults(result *models.ScanResult) {
	fmt.Printf("\n📊 Scan Results Summary:\n")
	fmt.Printf("🕒 Duration: %v\n", result.Duration)
	fmt.Printf("📁 Project: %s\n", result.ProjectPath)
	fmt.Printf("🔗 Chains: %s\n", strings.Join(result.Chains, ", "))
	fmt.Printf("📋 Total Issues: %d\n\n", len(result.Issues))

	// Display severity breakdown
	if len(result.SeverityCounts) > 0 {
		fmt.Println("🎯 Issues by Severity:")
		for severity, count := range result.SeverityCounts {
			if count > 0 {
				emoji := getSeverityEmoji(severity)
				fmt.Printf("  %s %s: %d\n", emoji, strings.Title(severity), count)
			}
		}
		fmt.Println()
	}

	// Display top issues
	if len(result.Issues) > 0 {
		fmt.Println("🔍 Top Issues Found:")
		displayCount := 5
		if len(result.Issues) < displayCount {
			displayCount = len(result.Issues)
		}

		for i := 0; i < displayCount; i++ {
			displayIssue(result.Issues[i])
		}

		if len(result.Issues) > displayCount {
			fmt.Printf("... and %d more issues\n", len(result.Issues)-displayCount)
		}
	}
}

func displayFileResults(filePath string, issues []models.SecurityIssue) {
	fmt.Printf("\n📊 File Audit Results:\n")
	fmt.Printf("📁 File: %s\n", filePath)
	fmt.Printf("📋 Issues Found: %d\n\n", len(issues))

	if len(issues) > 0 {
		for _, issue := range issues {
			displayIssue(issue)
		}
	} else {
		fmt.Println("✅ No security issues found in this file!")
	}
}

func displayIssue(issue models.SecurityIssue) {
	emoji := getSeverityEmoji(issue.Severity)
	fmt.Printf("%s [%s] %s\n", emoji, strings.ToUpper(issue.Severity), issue.Title)
	fmt.Printf("   📁 %s:%d\n", issue.File, issue.Line)
	fmt.Printf("   📝 %s\n", issue.Description)
	if issue.Suggestion != "" {
		fmt.Printf("   💡 %s\n", issue.Suggestion)
	}
	fmt.Println()
}

func getSeverityEmoji(severity string) string {
	switch strings.ToLower(severity) {
	case "critical":
		return "🚨"
	case "high":
		return "🔴"
	case "medium":
		return "🟡"
	case "low":
		return "🟢"
	case "info":
		return "ℹ️"
	default:
		return "❓"
	}
}

func filterKeyIssues(issues []models.SecurityIssue) []models.SecurityIssue {
	var keyIssues []models.SecurityIssue
	for _, issue := range issues {
		if issue.Type == "secret_leak" || issue.Type == "private_key_exposure" ||
			issue.Category == "wallet" || strings.Contains(issue.Description, "key") ||
			strings.Contains(issue.Description, "secret") {
			keyIssues = append(keyIssues, issue)
		}
	}
	return keyIssues
}

func filterEnvironmentIssues(issues []models.SecurityIssue) []models.SecurityIssue {
	var envIssues []models.SecurityIssue
	for _, issue := range issues {
		if issue.Category == "environment" || issue.Type == "environment_secret" ||
			strings.Contains(issue.File, ".env") || strings.Contains(issue.File, "docker") ||
			strings.Contains(issue.File, ".yml") || strings.Contains(issue.File, ".yaml") {
			envIssues = append(envIssues, issue)
		}
	}
	return envIssues
}

func saveResults(result *models.ScanResult, filename, format string) error {
	var content []byte
	var err error

	switch format {
	case "json":
		content, err = json.Marshal(result)
	case "markdown":
		content = []byte(generateMarkdownReport(result))
	default:
		return fmt.Errorf("unsupported format: %s", format)
	}

	if err != nil {
		return err
	}

	return os.WriteFile(filename, content, 0644)
}

func generateMarkdownReport(result *models.ScanResult) string {
	var report strings.Builder

	report.WriteString("# Security Scan Report\n\n")
	report.WriteString(fmt.Sprintf("**Project:** %s\n", result.ProjectPath))
	report.WriteString(fmt.Sprintf("**Scan ID:** %s\n", result.ID))
	report.WriteString(fmt.Sprintf("**Duration:** %v\n", result.Duration))
	report.WriteString(fmt.Sprintf("**Chains:** %s\n\n", strings.Join(result.Chains, ", ")))

	report.WriteString("## Summary\n\n")
	report.WriteString(fmt.Sprintf("- **Total Issues:** %d\n", len(result.Issues)))

	for severity, count := range result.SeverityCounts {
		if count > 0 {
			report.WriteString(fmt.Sprintf("- **%s:** %d\n", strings.Title(severity), count))
		}
	}

	report.WriteString("\n## Issues\n\n")
	for _, issue := range result.Issues {
		report.WriteString(fmt.Sprintf("### %s\n", issue.Title))
		report.WriteString(fmt.Sprintf("**Severity:** %s\n", issue.Severity))
		report.WriteString(fmt.Sprintf("**File:** %s:%d\n", issue.File, issue.Line))
		report.WriteString(fmt.Sprintf("**Description:** %s\n", issue.Description))
		if issue.Suggestion != "" {
			report.WriteString(fmt.Sprintf("**Suggestion:** %s\n", issue.Suggestion))
		}
		report.WriteString("\n")
	}

	return report.String()
}

func generateSampleReport() string {
	return fmt.Sprintf(`# Security Report - %s

## Executive Summary
This report provides a comprehensive security analysis of the blockchain project.

## Scan Details
- **Date:** %s
- **Tool:** SPT v1.0.0
- **Format:** %s

## Findings
No previous scan data available. Run 'spt scan' first to generate detailed findings.

## Recommendations
1. Run a comprehensive security scan using 'spt scan'
2. Check for exposed keys using 'spt check-keys'
3. Verify environment security with 'spt secure-env'
4. Audit smart contracts with 'spt audit'

## Next Steps
- Implement recommended security measures
- Schedule regular security scans
- Monitor for new vulnerabilities
`, time.Now().Format("2006-01-02"), time.Now().Format("2006-01-02 15:04:05"), format)
}

func getFileExtension(format string) string {
	switch format {
	case "markdown":
		return "md"
	case "pdf":
		return "pdf"
	case "json":
		return "json"
	default:
		return "txt"
	}
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
