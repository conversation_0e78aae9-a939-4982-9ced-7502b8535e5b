package commands

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/pkg/bitcoin"
	"blockchain-spt/spt/backend/pkg/ethereum"

	"blockchain-spt/spt/backend/internal/config"

	"github.com/spf13/cobra"
)

// AnalyzeOptions represents analyze command options
type AnalyzeOptions struct {
	Path        string
	Chain       string
	Detailed    bool
	Metrics     bool
	Complexity  bool
	Performance bool
	Output      string
	Format      string
}

// NewAnalyzeCommand creates the analyze command
func NewAnalyzeCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "analyze",
		Short: "Detailed code analysis and metrics",
		Long: `Perform detailed code analysis and generate metrics for blockchain applications.

The analyze command provides in-depth analysis including:
- Code complexity metrics
- Security pattern analysis
- Performance optimization suggestions
- Best practice compliance
- Gas optimization opportunities (Ethereum)`,
	}

	// Add subcommands
	cmd.AddCommand(newAnalyzeEthereumCommand())
	cmd.AddCommand(newAnalyzeBitcoinCommand())
	cmd.AddCommand(newAnalyzeContractCommand())

	return cmd
}

// newAnalyzeEthereumCommand creates the ethereum analyze subcommand
func newAnalyzeEthereumCommand() *cobra.Command {
	opts := &AnalyzeOptions{}

	cmd := &cobra.Command{
		Use:   "ethereum [path]",
		Short: "Analyze Ethereum smart contracts",
		Long: `Perform detailed analysis of Ethereum smart contracts and DApps.

This command provides:
- Gas optimization analysis
- Contract complexity metrics
- Security pattern detection
- Best practice compliance
- Performance recommendations

Examples:
  spt analyze ethereum ./contracts           # Analyze all contracts
  spt analyze ethereum --detailed ./token   # Detailed analysis
  spt analyze ethereum --metrics ./src      # Include metrics`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			opts.Chain = "ethereum"
			return runAnalyze(cmd, opts)
		},
	}

	addAnalyzeFlags(cmd, opts)
	return cmd
}

// newAnalyzeBitcoinCommand creates the bitcoin analyze subcommand
func newAnalyzeBitcoinCommand() *cobra.Command {
	opts := &AnalyzeOptions{}

	cmd := &cobra.Command{
		Use:   "bitcoin [path]",
		Short: "Analyze Bitcoin scripts and implementations",
		Long: `Perform detailed analysis of Bitcoin scripts and wallet implementations.

This command provides:
- Script complexity analysis
- UTXO pattern analysis
- Security best practices
- Performance optimization
- Wallet security metrics

Examples:
  spt analyze bitcoin ./wallet               # Analyze wallet code
  spt analyze bitcoin --detailed ./scripts  # Detailed script analysis
  spt analyze bitcoin --metrics ./btc       # Include metrics`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			opts.Chain = "bitcoin"
			return runAnalyze(cmd, opts)
		},
	}

	addAnalyzeFlags(cmd, opts)
	return cmd
}

// newAnalyzeContractCommand creates the contract analyze subcommand
func newAnalyzeContractCommand() *cobra.Command {
	opts := &AnalyzeOptions{}

	cmd := &cobra.Command{
		Use:   "contract [path]",
		Short: "Analyze smart contracts (all chains)",
		Long: `Perform detailed analysis of smart contracts across all supported blockchains.

This command provides:
- Cross-chain compatibility analysis
- Security pattern detection
- Code quality metrics
- Performance optimization
- Best practice recommendations

Examples:
  spt analyze contract ./contracts           # Analyze all contracts
  spt analyze contract --chain ethereum ./src # Ethereum only
  spt analyze contract --detailed ./token   # Detailed analysis`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runAnalyze(cmd, opts)
		},
	}

	addAnalyzeFlags(cmd, opts)
	cmd.Flags().StringVar(&opts.Chain, "chain", "", "Specific blockchain to analyze (ethereum, bitcoin)")

	return cmd
}

// addAnalyzeFlags adds common analyze flags to a command
func addAnalyzeFlags(cmd *cobra.Command, opts *AnalyzeOptions) {
	cmd.Flags().BoolVar(&opts.Detailed, "detailed", false, "Detailed analysis with extended metrics")
	cmd.Flags().BoolVar(&opts.Metrics, "metrics", false, "Include code metrics")
	cmd.Flags().BoolVar(&opts.Complexity, "complexity", false, "Include complexity analysis")
	cmd.Flags().BoolVar(&opts.Performance, "performance", false, "Include performance analysis")
	cmd.Flags().StringVarP(&opts.Output, "output", "o", "", "Output file path")
	cmd.Flags().StringVarP(&opts.Format, "format", "f", "table", "Output format (table, json, yaml)")
}

// runAnalyze executes the analyze command
func runAnalyze(cmd *cobra.Command, opts *AnalyzeOptions) error {
	// Load configuration
	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Validate path
	if _, err := os.Stat(opts.Path); os.IsNotExist(err) {
		return fmt.Errorf("path does not exist: %s", opts.Path)
	}

	fmt.Printf("🔍 Starting detailed analysis of: %s\n", opts.Path)
	fmt.Printf("📊 Analysis configuration:\n")
	fmt.Printf("   - Chain: %s\n", getChainDisplay(opts.Chain))
	fmt.Printf("   - Detailed: %v\n", opts.Detailed)
	fmt.Printf("   - Metrics: %v\n", opts.Metrics)
	fmt.Printf("   - Complexity: %v\n", opts.Complexity)
	fmt.Printf("   - Performance: %v\n", opts.Performance)
	fmt.Printf("\n")

	startTime := time.Now()

	// Perform analysis based on chain
	var issues []models.SecurityIssue
	var analysisErr error

	switch strings.ToLower(opts.Chain) {
	case "ethereum":
		issues, analysisErr = analyzeEthereum(cfg, opts)
	case "bitcoin":
		issues, analysisErr = analyzeBitcoin(cfg, opts)
	case "":
		// Analyze all supported chains
		ethIssues, ethErr := analyzeEthereum(cfg, opts)
		if ethErr != nil {
			fmt.Printf("⚠️  Ethereum analysis warning: %v\n", ethErr)
		} else {
			issues = append(issues, ethIssues...)
		}

		btcIssues, btcErr := analyzeBitcoin(cfg, opts)
		if btcErr != nil {
			fmt.Printf("⚠️  Bitcoin analysis warning: %v\n", btcErr)
		} else {
			issues = append(issues, btcIssues...)
		}
	default:
		return fmt.Errorf("unsupported chain: %s", opts.Chain)
	}

	if analysisErr != nil {
		return fmt.Errorf("analysis failed: %w", analysisErr)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ Analysis completed in %v\n", duration)

	// Display results
	displayOptions := &ScanOptions{
		Format:   opts.Format,
		Severity: "",
		Output:   opts.Output,
	}

	if err := displayResults(issues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Display analysis-specific information
	displayAnalysisMetrics(issues, opts)

	// Save output if specified
	if opts.Output != "" {
		if err := saveResults(issues, displayOptions); err != nil {
			return fmt.Errorf("failed to save results: %w", err)
		}
		fmt.Printf("📄 Analysis results saved to: %s\n", opts.Output)
	}

	return nil
}

// analyzeEthereum performs Ethereum-specific analysis
func analyzeEthereum(cfg *config.Config, opts *AnalyzeOptions) ([]models.SecurityIssue, error) {
	fmt.Printf("🔎 Running Ethereum code analysis...\n")

	scanner, err := ethereum.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Ethereum scanner: %w", err)
	}

	ctx := context.Background()

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return nil, fmt.Errorf("failed to stat path: %w", err)
	}

	var issues []models.SecurityIssue
	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return nil, err
	}

	fmt.Printf("   Found %d Ethereum-related issues\n", len(issues))
	return issues, nil
}

// analyzeBitcoin performs Bitcoin-specific analysis
func analyzeBitcoin(cfg *config.Config, opts *AnalyzeOptions) ([]models.SecurityIssue, error) {
	fmt.Printf("🔎 Running Bitcoin code analysis...\n")

	scanner, err := bitcoin.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Bitcoin scanner: %w", err)
	}

	ctx := context.Background()

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return nil, fmt.Errorf("failed to stat path: %w", err)
	}

	var issues []models.SecurityIssue
	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return nil, err
	}

	fmt.Printf("   Found %d Bitcoin-related issues\n", len(issues))
	return issues, nil
}

// displayAnalysisMetrics displays analysis-specific metrics and insights
func displayAnalysisMetrics(issues []models.SecurityIssue, opts *AnalyzeOptions) {
	fmt.Printf("\n📊 Analysis Metrics\n")
	fmt.Printf("═══════════════════\n")

	if len(issues) == 0 {
		fmt.Printf("🎉 No issues found during analysis!\n")
		return
	}

	// Basic metrics
	displayBasicMetrics(issues)

	// Detailed metrics if requested
	if opts.Metrics {
		displayDetailedMetrics(issues)
	}

	// Complexity analysis if requested
	if opts.Complexity {
		displayComplexityAnalysis(issues)
	}

	// Performance analysis if requested
	if opts.Performance {
		displayPerformanceAnalysis(issues, opts.Chain)
	}

	// Recommendations
	displayAnalysisRecommendations(issues, opts)
}

// displayBasicMetrics shows basic analysis metrics
func displayBasicMetrics(issues []models.SecurityIssue) {
	// Count issues by type
	typeCounts := make(map[string]int)
	for _, issue := range issues {
		typeCounts[issue.Type]++
	}

	fmt.Printf("Issue Distribution:\n")
	for issueType, count := range typeCounts {
		fmt.Printf("  • %s: %d\n", strings.Title(strings.ReplaceAll(issueType, "_", " ")), count)
	}
}

// displayDetailedMetrics shows detailed analysis metrics
func displayDetailedMetrics(issues []models.SecurityIssue) {
	fmt.Printf("\nDetailed Metrics:\n")

	// File distribution
	fileCounts := make(map[string]int)
	for _, issue := range issues {
		fileCounts[issue.File]++
	}

	fmt.Printf("Files with Issues: %d\n", len(fileCounts))

	// Most problematic files
	if len(fileCounts) > 0 {
		fmt.Printf("Most Issues per File:\n")
		// This would show top files with most issues
		for file, count := range fileCounts {
			if count > 1 {
				fmt.Printf("  • %s: %d issues\n", file, count)
			}
		}
	}
}

// displayComplexityAnalysis shows complexity analysis
func displayComplexityAnalysis(issues []models.SecurityIssue) {
	fmt.Printf("\nComplexity Analysis:\n")

	// This would analyze code complexity based on issues found
	complexityIssues := 0
	for _, issue := range issues {
		if strings.Contains(strings.ToLower(issue.Type), "complex") ||
			strings.Contains(strings.ToLower(issue.Description), "complex") {
			complexityIssues++
		}
	}

	if complexityIssues > 0 {
		fmt.Printf("  • Complexity-related issues: %d\n", complexityIssues)
		fmt.Printf("  • Recommendation: Consider refactoring complex functions\n")
	} else {
		fmt.Printf("  • No significant complexity issues detected\n")
	}
}

// displayPerformanceAnalysis shows performance analysis
func displayPerformanceAnalysis(issues []models.SecurityIssue, chain string) {
	fmt.Printf("\nPerformance Analysis:\n")

	performanceIssues := 0
	gasIssues := 0

	for _, issue := range issues {
		if strings.Contains(strings.ToLower(issue.Type), "gas") ||
			strings.Contains(strings.ToLower(issue.Type), "performance") {
			performanceIssues++
			if strings.Contains(strings.ToLower(issue.Type), "gas") {
				gasIssues++
			}
		}
	}

	if chain == "ethereum" && gasIssues > 0 {
		fmt.Printf("  • Gas optimization opportunities: %d\n", gasIssues)
		fmt.Printf("  • Potential gas savings available\n")
	}

	if performanceIssues > 0 {
		fmt.Printf("  • Performance-related issues: %d\n", performanceIssues)
	} else {
		fmt.Printf("  • No significant performance issues detected\n")
	}
}

// displayAnalysisRecommendations shows analysis-based recommendations
func displayAnalysisRecommendations(issues []models.SecurityIssue, opts *AnalyzeOptions) {
	fmt.Printf("\n💡 Analysis Recommendations:\n")

	if len(issues) == 0 {
		fmt.Printf("  ✅ Code appears to follow security best practices\n")
		fmt.Printf("  ✅ Consider regular security audits\n")
		fmt.Printf("  ✅ Keep dependencies updated\n")
		return
	}

	// Count critical and high severity issues
	critical := 0
	high := 0

	for _, issue := range issues {
		switch strings.ToLower(issue.Severity) {
		case "critical":
			critical++
		case "high":
			high++
		}
	}

	if critical > 0 {
		fmt.Printf("  🚨 Address %d critical issues immediately\n", critical)
	}

	if high > 0 {
		fmt.Printf("  ⚠️  Review %d high severity issues\n", high)
	}

	// Chain-specific recommendations
	switch strings.ToLower(opts.Chain) {
	case "ethereum":
		fmt.Printf("  ⟠ Consider gas optimization for cost efficiency\n")
		fmt.Printf("  ⟠ Implement proper access controls\n")
		fmt.Printf("  ⟠ Use latest Solidity version and best practices\n")
	case "bitcoin":
		fmt.Printf("  ₿ Ensure proper UTXO management\n")
		fmt.Printf("  ₿ Validate all script operations\n")
		fmt.Printf("  ₿ Use secure key management practices\n")
	}

	fmt.Printf("  📚 Regular code reviews and testing recommended\n")
	fmt.Printf("  🔄 Consider automated security scanning in CI/CD\n")
}
