package ethereum

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"

	"github.com/sirupsen/logrus"
)

// GasAnalyzer analyzes smart contracts for gas optimization opportunities
type GasAnalyzer struct {
	logger *logrus.Logger
}

// GasOptimization represents a gas optimization opportunity
type GasOptimization struct {
	Type        string
	Description string
	Severity    string
	Savings     int // Estimated gas savings
	Line        int
	Code        string
	Suggestion  string
}

// NewGasAnalyzer creates a new gas analyzer instance
func NewGasAnalyzer() *GasAnalyzer {
	return &GasAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzeGasOptimizations analyzes a contract for gas optimization opportunities
func (ga *GasAnalyzer) AnalyzeGasOptimizations(filePath string, content string, ast *ContractAST) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Run all gas optimization checks
	optimizations := []func(string, string, *ContractAST) []GasOptimization{
		ga.checkStorageOptimizations,
		ga.checkLoopOptimizations,
		ga.checkFunctionVisibility,
		ga.checkConstantVariables,
		ga.checkPackedStructs,
		ga.checkRedundantOperations,
		ga.checkStringComparisons,
		ga.checkArrayLengthCaching,
		ga.checkUnnecessaryVariables,
		ga.checkEventOptimizations,
	}

	for _, check := range optimizations {
		opts := check(filePath, content, ast)
		for _, opt := range opts {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gas_opt_%s_%d", opt.Type, opt.Line),
				Type:        "gas_optimization",
				Severity:    opt.Severity,
				Title:       fmt.Sprintf("Gas Optimization: %s", opt.Type),
				Description: opt.Description,
				File:        filePath,
				Line:        opt.Line,
				Code:        opt.Code,
				Chain:       "ethereum",
				Category:    "smart_contract",
				Suggestion:  opt.Suggestion,
				Metadata: map[string]string{
					"estimated_savings": strconv.Itoa(opt.Savings),
					"optimization_type": opt.Type,
				},
				References: []string{
					"https://docs.soliditylang.org/en/latest/internals/optimiser.html",
					"https://github.com/ethereum/solidity/blob/develop/docs/gas-costs.md",
				},
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			})
		}
	}

	return issues, nil
}

// checkStorageOptimizations checks for storage-related optimizations
func (ga *GasAnalyzer) checkStorageOptimizations(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	// Check for unnecessary storage reads
	storageReadPattern := regexp.MustCompile(`(\w+)\[([^\]]+)\]`)

	for i, line := range lines {
		matches := storageReadPattern.FindAllStringSubmatch(line, -1)
		if len(matches) > 1 {
			// Multiple reads from the same storage location
			storageVars := make(map[string]int)
			for _, match := range matches {
				if len(match) >= 2 {
					storageVars[match[1]]++
				}
			}

			for varName, count := range storageVars {
				if count > 1 {
					optimizations = append(optimizations, GasOptimization{
						Type:        "storage_caching",
						Description: fmt.Sprintf("Multiple reads from storage variable '%s' in same function", varName),
						Severity:    "low",
						Savings:     (count - 1) * 100, // ~100 gas per SLOAD saved
						Line:        i + 1,
						Code:        strings.TrimSpace(line),
						Suggestion:  fmt.Sprintf("Cache '%s' in memory variable to save gas", varName),
					})
				}
			}
		}
	}

	// Check for storage vs memory usage
	storagePattern := regexp.MustCompile(`(\w+)\s+storage\s+(\w+)`)
	for i, line := range lines {
		if storagePattern.MatchString(line) && strings.Contains(line, "function") {
			optimizations = append(optimizations, GasOptimization{
				Type:        "storage_vs_memory",
				Description: "Consider using memory instead of storage for temporary variables",
				Severity:    "low",
				Savings:     200, // Approximate savings
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  "Use 'memory' instead of 'storage' for read-only temporary variables",
			})
		}
	}

	return optimizations
}

// checkLoopOptimizations checks for loop-related optimizations
func (ga *GasAnalyzer) checkLoopOptimizations(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	// Check for array.length in loop condition
	lengthInLoopPattern := regexp.MustCompile(`for\s*\([^;]*;\s*\w+\s*<\s*(\w+)\.length`)

	for i, line := range lines {
		matches := lengthInLoopPattern.FindStringSubmatch(line)
		if len(matches) > 1 {
			optimizations = append(optimizations, GasOptimization{
				Type:        "loop_length_caching",
				Description: "Array length accessed in every loop iteration",
				Severity:    "medium",
				Savings:     50, // ~50 gas per iteration saved
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  fmt.Sprintf("Cache %s.length in a local variable before the loop", matches[1]),
			})
		}
	}

	// Check for ++i vs i++
	postIncrementPattern := regexp.MustCompile(`\w+\+\+`)
	preIncrementPattern := regexp.MustCompile(`\+\+\w+`)

	for i, line := range lines {
		if strings.Contains(line, "for") && postIncrementPattern.MatchString(line) && !preIncrementPattern.MatchString(line) {
			optimizations = append(optimizations, GasOptimization{
				Type:        "pre_increment",
				Description: "Post-increment uses more gas than pre-increment",
				Severity:    "low",
				Savings:     5, // Small but consistent savings
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  "Use ++i instead of i++ in loops",
			})
		}
	}

	return optimizations
}

// checkFunctionVisibility checks for function visibility optimizations
func (ga *GasAnalyzer) checkFunctionVisibility(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization

	for _, function := range ast.Functions {
		if function.Visibility == "public" && !function.IsConstructor {
			// Check if function is called internally
			functionCallPattern := regexp.MustCompile(fmt.Sprintf(`\b%s\s*\(`, function.Name))
			internalCalls := functionCallPattern.FindAllString(content, -1)

			if len(internalCalls) > 0 {
				optimizations = append(optimizations, GasOptimization{
					Type:        "function_visibility",
					Description: fmt.Sprintf("Function '%s' could be external instead of public", function.Name),
					Severity:    "low",
					Savings:     20, // Approximate savings
					Line:        function.Location.Line,
					Code:        fmt.Sprintf("function %s", function.Name),
					Suggestion:  "Change visibility from 'public' to 'external' if function is not called internally",
				})
			}
		}
	}

	return optimizations
}

// checkConstantVariables checks for variables that could be constant
func (ga *GasAnalyzer) checkConstantVariables(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization

	for _, stateVar := range ast.StateVars {
		if !stateVar.Constant && !stateVar.Immutable {
			// Check if variable is never modified
			assignmentPattern := regexp.MustCompile(fmt.Sprintf(`%s\s*=`, stateVar.Name))
			assignments := assignmentPattern.FindAllString(content, -1)

			// If only one assignment (declaration), it could be constant
			if len(assignments) <= 1 {
				optimizations = append(optimizations, GasOptimization{
					Type:        "constant_variable",
					Description: fmt.Sprintf("State variable '%s' could be declared as constant", stateVar.Name),
					Severity:    "low",
					Savings:     15000, // Significant savings for constant variables
					Line:        stateVar.Location.Line,
					Code:        fmt.Sprintf("%s %s", stateVar.Type, stateVar.Name),
					Suggestion:  "Add 'constant' keyword if the variable value never changes",
				})
			}
		}
	}

	return optimizations
}

// checkPackedStructs checks for struct packing optimizations
func (ga *GasAnalyzer) checkPackedStructs(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	structPattern := regexp.MustCompile(`struct\s+(\w+)\s*\{`)

	for i, line := range lines {
		matches := structPattern.FindStringSubmatch(line)
		if len(matches) > 1 {
			structName := matches[1]

			// Analyze struct fields for packing opportunities
			structEnd := ga.findStructEnd(lines, i)
			if structEnd > i {
				fields := ga.extractStructFields(lines[i:structEnd])
				if ga.canOptimizeStructPacking(fields) {
					optimizations = append(optimizations, GasOptimization{
						Type:        "struct_packing",
						Description: fmt.Sprintf("Struct '%s' fields could be reordered for better packing", structName),
						Severity:    "medium",
						Savings:     2000, // One storage slot saved
						Line:        i + 1,
						Code:        strings.TrimSpace(line),
						Suggestion:  "Reorder struct fields to pack them into fewer storage slots",
					})
				}
			}
		}
	}

	return optimizations
}

// checkRedundantOperations checks for redundant operations
func (ga *GasAnalyzer) checkRedundantOperations(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	// Check for x = x + y patterns that could use x += y
	redundantAssignPattern := regexp.MustCompile(`(\w+)\s*=\s*\1\s*[\+\-\*\/]\s*(.+)`)

	for i, line := range lines {
		matches := redundantAssignPattern.FindStringSubmatch(line)
		if len(matches) > 2 {
			optimizations = append(optimizations, GasOptimization{
				Type:        "compound_assignment",
				Description: "Use compound assignment operators for gas efficiency",
				Severity:    "low",
				Savings:     10, // Small savings
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  fmt.Sprintf("Use %s += %s instead of %s = %s + %s", matches[1], matches[2], matches[1], matches[1], matches[2]),
			})
		}
	}

	return optimizations
}

// checkStringComparisons checks for inefficient string comparisons
func (ga *GasAnalyzer) checkStringComparisons(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	stringComparePattern := regexp.MustCompile(`keccak256\(.*\)\s*==\s*keccak256\(.*\)`)

	for i, line := range lines {
		if stringComparePattern.MatchString(line) {
			optimizations = append(optimizations, GasOptimization{
				Type:        "string_comparison",
				Description: "String comparison using keccak256 is gas expensive",
				Severity:    "medium",
				Savings:     100, // Approximate savings
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  "Consider using bytes32 for string comparisons or implement more efficient comparison logic",
			})
		}
	}

	return optimizations
}

// checkArrayLengthCaching checks for array length caching opportunities
func (ga *GasAnalyzer) checkArrayLengthCaching(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	lengthAccessPattern := regexp.MustCompile(`(\w+)\.length`)

	for i, line := range lines {
		matches := lengthAccessPattern.FindAllStringSubmatch(line, -1)
		if len(matches) > 1 {
			// Multiple length accesses in same line
			optimizations = append(optimizations, GasOptimization{
				Type:        "array_length_caching",
				Description: "Multiple array length accesses in same expression",
				Severity:    "low",
				Savings:     30, // ~30 gas per extra length access
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Suggestion:  "Cache array length in a local variable",
			})
		}
	}

	return optimizations
}

// checkUnnecessaryVariables checks for unnecessary variable declarations
func (ga *GasAnalyzer) checkUnnecessaryVariables(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization
	lines := strings.Split(content, "\n")

	// Check for variables that are assigned once and used once
	varDeclPattern := regexp.MustCompile(`(\w+)\s+(\w+)\s*=\s*([^;]+);`)

	for i, line := range lines {
		matches := varDeclPattern.FindStringSubmatch(line)
		if len(matches) > 3 {
			varName := matches[2]

			// Count usage of this variable
			varUsagePattern := regexp.MustCompile(fmt.Sprintf(`\b%s\b`, varName))
			usageCount := len(varUsagePattern.FindAllString(content, -1))

			// If used only twice (declaration + one usage), it might be unnecessary
			if usageCount == 2 {
				optimizations = append(optimizations, GasOptimization{
					Type:        "unnecessary_variable",
					Description: fmt.Sprintf("Variable '%s' is used only once and could be inlined", varName),
					Severity:    "low",
					Savings:     20, // Small savings from avoiding variable declaration
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Suggestion:  "Consider inlining the expression instead of using a temporary variable",
				})
			}
		}
	}

	return optimizations
}

// checkEventOptimizations checks for event-related optimizations
func (ga *GasAnalyzer) checkEventOptimizations(filePath string, content string, ast *ContractAST) []GasOptimization {
	var optimizations []GasOptimization

	for _, event := range ast.Events {
		// Check if event parameters could be indexed
		nonIndexedParams := 0
		for _, param := range event.Parameters {
			// This is a simplified check - in real implementation, we'd parse the event declaration
			if !strings.Contains(param.Type, "indexed") {
				nonIndexedParams++
			}
		}

		if nonIndexedParams > 3 && len(event.Parameters) > 3 {
			optimizations = append(optimizations, GasOptimization{
				Type:        "event_indexing",
				Description: fmt.Sprintf("Event '%s' could benefit from indexed parameters", event.Name),
				Severity:    "low",
				Savings:     50, // Approximate savings for filtering
				Line:        event.Location.Line,
				Code:        fmt.Sprintf("event %s", event.Name),
				Suggestion:  "Add 'indexed' keyword to frequently filtered event parameters (max 3)",
			})
		}
	}

	return optimizations
}

// Helper functions

func (ga *GasAnalyzer) findStructEnd(lines []string, start int) int {
	braceCount := 0
	for i := start; i < len(lines); i++ {
		line := lines[i]
		braceCount += strings.Count(line, "{")
		braceCount -= strings.Count(line, "}")
		if braceCount == 0 && i > start {
			return i + 1
		}
	}
	return len(lines)
}

func (ga *GasAnalyzer) extractStructFields(lines []string) []string {
	var fields []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, ";") && !strings.Contains(line, "struct") && !strings.Contains(line, "}") {
			fields = append(fields, line)
		}
	}
	return fields
}

func (ga *GasAnalyzer) canOptimizeStructPacking(fields []string) bool {
	// Simplified check for struct packing optimization
	// In a real implementation, this would analyze field sizes and alignment
	hasSmallTypes := false
	hasLargeTypes := false

	for _, field := range fields {
		if strings.Contains(field, "uint8") || strings.Contains(field, "bool") || strings.Contains(field, "uint16") {
			hasSmallTypes = true
		}
		if strings.Contains(field, "uint256") || strings.Contains(field, "address") || strings.Contains(field, "bytes32") {
			hasLargeTypes = true
		}
	}

	// If we have both small and large types, there might be packing opportunities
	return hasSmallTypes && hasLargeTypes && len(fields) > 2
}
