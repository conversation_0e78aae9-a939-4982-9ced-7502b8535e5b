package ethereum

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/spt/backend/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test contract samples
const vulnerableContract = `
pragma solidity ^0.7.0;

contract VulnerableContract {
    mapping(address => uint256) public balances;
    
    function withdraw(uint256 amount) public {
        require(balances[msg.sender] >= amount);
        msg.sender.call{value: amount}("");
        balances[msg.sender] -= amount;
    }
    
    function unsafeAdd(uint256 a, uint256 b) public pure returns (uint256) {
        return a + b; // No overflow protection
    }
    
    function publicFunction() public {
        // No access control
        balances[msg.sender] = 1000000;
    }
}
`

const secureContract = `
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract SecureContract is ReentrancyGuard, Ownable {
    mapping(address => uint256) public balances;
    
    function withdraw(uint256 amount) public nonReentrant {
        require(balances[msg.sender] >= amount, "Insufficient balance");
        balances[msg.sender] -= amount;
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success, "Transfer failed");
    }
    
    function safeAdd(uint256 a, uint256 b) public pure returns (uint256) {
        return a + b; // Solidity 0.8+ has built-in overflow protection
    }
    
    function adminFunction() public onlyOwner {
        // Proper access control
        balances[msg.sender] = 1000000;
    }
}
`

const gasInefficient = `
pragma solidity ^0.8.0;

contract GasInefficient {
    uint256[] public items;
    mapping(address => uint256) public balances;
    
    struct User {
        uint256 balance;
        uint8 level;
        uint256 timestamp;
        bool active;
    }
    
    function inefficientLoop() public {
        for (uint256 i = 0; i < items.length; i++) {
            items[i] = items[i] + 1;
        }
    }
    
    function redundantReads(address user) public view returns (uint256) {
        return balances[user] + balances[user] + balances[user];
    }
    
    function stringComparison(string memory a, string memory b) public pure returns (bool) {
        return keccak256(abi.encodePacked(a)) == keccak256(abi.encodePacked(b));
    }
}
`

func TestScanner_ScanFile(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
			Rules: config.SecurityRulesConfig{
				SmartContractAudit: config.ContractConfig{
					Enabled: true,
					Ethereum: config.EthereumConfig{
						Reentrancy:      true,
						IntegerOverflow: true,
						UncheckedCalls:  true,
						AccessControl:   true,
						GasOptimization: true,
					},
				},
			},
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	tests := []struct {
		name           string
		contract       string
		expectedIssues []string
		minIssues      int
	}{
		{
			name:     "Vulnerable Contract",
			contract: vulnerableContract,
			expectedIssues: []string{
				"reentrancy",
				"integer_overflow",
				"access_control",
			},
			minIssues: 3,
		},
		{
			name:      "Secure Contract",
			contract:  secureContract,
			minIssues: 0, // Should have minimal issues
		},
		{
			name:     "Gas Inefficient Contract",
			contract: gasInefficient,
			expectedIssues: []string{
				"gas_optimization",
			},
			minIssues: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tmpFile, err := createTempSolidityFile(tt.contract)
			require.NoError(t, err)
			defer os.Remove(tmpFile)

			// Scan the file
			issues, err := scanner.ScanFile(context.Background(), tmpFile)
			require.NoError(t, err)

			// Check minimum number of issues
			assert.GreaterOrEqual(t, len(issues), tt.minIssues, "Expected at least %d issues, got %d", tt.minIssues, len(issues))

			// Check for expected issue types
			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expectedIssues {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}

			// Verify issue structure
			for _, issue := range issues {
				assert.NotEmpty(t, issue.ID)
				assert.NotEmpty(t, issue.Type)
				assert.NotEmpty(t, issue.Severity)
				assert.NotEmpty(t, issue.Title)
				assert.NotEmpty(t, issue.Description)
				assert.Equal(t, tmpFile, issue.File)
				assert.Equal(t, "ethereum", issue.Chain)
				assert.Greater(t, issue.Line, 0)
			}
		})
	}
}

func TestVulnerabilityDetector_DetectReentrancy(t *testing.T) {
	detector := NewVulnerabilityDetector()

	tests := []struct {
		name     string
		code     string
		expected bool
	}{
		{
			name: "Reentrancy Vulnerability",
			code: `
				function withdraw() public {
					msg.sender.call{value: amount}("");
					balance = 0;
				}
			`,
			expected: true,
		},
		{
			name: "Safe Pattern",
			code: `
				function withdraw() public {
					balance = 0;
					msg.sender.call{value: amount}("");
				}
			`,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := detector.DetectVulnerabilities("test.sol", tt.code)
			require.NoError(t, err)

			hasReentrancy := false
			for _, issue := range issues {
				if issue.Type == "reentrancy" {
					hasReentrancy = true
					break
				}
			}

			assert.Equal(t, tt.expected, hasReentrancy)
		})
	}
}

func TestGasAnalyzer_AnalyzeOptimizations(t *testing.T) {
	analyzer := NewGasAnalyzer()
	parser := NewASTParser()

	tests := []struct {
		name         string
		code         string
		expectedOpts []string
	}{
		{
			name: "Loop Length Caching",
			code: `
				function inefficientLoop() public {
					for (uint i = 0; i < items.length; i++) {
						items[i] = 0;
					}
				}
			`,
			expectedOpts: []string{"loop_length_caching"},
		},
		{
			name: "Storage Caching",
			code: `
				function multipleReads() public {
					uint a = balances[msg.sender];
					uint b = balances[msg.sender];
				}
			`,
			expectedOpts: []string{"storage_caching"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ast, err := parser.parseWithRegex("test.sol")
			if err != nil {
				// Create minimal AST for testing
				ast = &ContractAST{}
			}

			issues, err := analyzer.AnalyzeGasOptimizations("test.sol", tt.code, ast)
			require.NoError(t, err)

			optimizationTypes := make(map[string]bool)
			for _, issue := range issues {
				if optType, exists := issue.Metadata["optimization_type"]; exists {
					optimizationTypes[optType] = true
				}
			}

			for _, expectedOpt := range tt.expectedOpts {
				assert.True(t, optimizationTypes[expectedOpt], "Expected optimization %s not found", expectedOpt)
			}
		})
	}
}

func TestContractAnalyzer_AnalyzeContract(t *testing.T) {
	analyzer := NewContractAnalyzer()

	tmpFile, err := createTempSolidityFile(vulnerableContract)
	require.NoError(t, err)
	defer os.Remove(tmpFile)

	contractInfo, err := analyzer.AnalyzeContract(tmpFile, vulnerableContract)
	require.NoError(t, err)

	assert.NotEmpty(t, contractInfo.ID)
	assert.Equal(t, "VulnerableContract", contractInfo.Name)
	assert.Equal(t, tmpFile, contractInfo.File)
	assert.Equal(t, "ethereum", contractInfo.Chain)
	assert.Equal(t, "solidity", contractInfo.Language)
	assert.Greater(t, contractInfo.Complexity, 0)
	assert.Greater(t, len(contractInfo.Functions), 0)
}

func TestASTParser_ParseContract(t *testing.T) {
	parser := NewASTParser()

	tmpFile, err := createTempSolidityFile(secureContract)
	require.NoError(t, err)
	defer os.Remove(tmpFile)

	ast, err := parser.ParseContract(tmpFile)
	require.NoError(t, err)

	// Check that we parsed some basic elements
	assert.Greater(t, len(ast.Contracts), 0, "Should parse at least one contract")
	assert.Greater(t, len(ast.Functions), 0, "Should parse at least one function")
	assert.Greater(t, len(ast.Imports), 0, "Should parse import statements")

	// Check pragma parsing
	foundSolidityPragma := false
	for _, pragma := range ast.Pragmas {
		if pragma.Name == "solidity" {
			foundSolidityPragma = true
			assert.Contains(t, pragma.Value, "0.8.0")
		}
	}
	assert.True(t, foundSolidityPragma, "Should parse solidity pragma")

	// Check contract parsing
	foundContract := false
	for _, contract := range ast.Contracts {
		if contract.Name == "SecureContract" {
			foundContract = true
			assert.Equal(t, "contract", contract.Kind)
			assert.Contains(t, contract.Inheritance, "ReentrancyGuard")
			assert.Contains(t, contract.Inheritance, "Ownable")
		}
	}
	assert.True(t, foundContract, "Should parse SecureContract")
}

func TestScanner_ScanProject(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
		Scanning: config.ScanningConfig{
			Paths: config.PathsConfig{
				Include: []string{"*.sol"},
				Exclude: []string{"node_modules"},
			},
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	// Create temporary directory with contracts
	tmpDir, err := os.MkdirTemp("", "ethereum_test")
	require.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	// Create test contracts
	contracts := map[string]string{
		"Vulnerable.sol": vulnerableContract,
		"Secure.sol":     secureContract,
		"Gas.sol":        gasInefficient,
	}

	for filename, content := range contracts {
		filePath := filepath.Join(tmpDir, filename)
		err := os.WriteFile(filePath, []byte(content), 0644)
		require.NoError(t, err)
	}

	// Scan the project
	issues, err := scanner.ScanProject(context.Background(), tmpDir)
	require.NoError(t, err)

	// Should find issues across multiple files
	assert.Greater(t, len(issues), 0, "Should find security issues in the project")

	// Check that issues are from different files
	files := make(map[string]bool)
	for _, issue := range issues {
		files[filepath.Base(issue.File)] = true
	}

	// Should have issues from vulnerable contract at minimum
	assert.True(t, files["Vulnerable.sol"], "Should find issues in Vulnerable.sol")
}

// Helper functions

func createTempSolidityFile(content string) (string, error) {
	tmpFile, err := os.CreateTemp("", "test_*.sol")
	if err != nil {
		return "", err
	}

	_, err = tmpFile.WriteString(content)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return "", err
	}

	tmpFile.Close()
	return tmpFile.Name(), nil
}

// Benchmark tests

func BenchmarkScanner_ScanFile(b *testing.B) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(b, err)

	tmpFile, err := createTempSolidityFile(vulnerableContract)
	require.NoError(b, err)
	defer os.Remove(tmpFile)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := scanner.ScanFile(context.Background(), tmpFile)
		require.NoError(b, err)
	}
}

func BenchmarkVulnerabilityDetector_DetectVulnerabilities(b *testing.B) {
	detector := NewVulnerabilityDetector()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := detector.DetectVulnerabilities("test.sol", vulnerableContract)
		require.NoError(b, err)
	}
}
