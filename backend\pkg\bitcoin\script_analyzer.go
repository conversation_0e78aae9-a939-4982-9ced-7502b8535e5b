package bitcoin

import (
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"

	"github.com/sirupsen/logrus"
)

// ScriptAnalyzer analyzes Bitcoin scripts for security issues
type ScriptAnalyzer struct {
	logger *logrus.Logger
}

// ScriptType represents different types of Bitcoin scripts
type ScriptType int

const (
	ScriptTypeUnknown     ScriptType = iota
	ScriptTypeP2PK                   // Pay to Public Key
	ScriptTypeP2PKH                  // Pay to Public Key Hash
	ScriptTypeP2SH                   // Pay to Script Hash
	ScriptTypeP2WPKH                 // Pay to Witness Public Key Hash
	ScriptTypeP2WSH                  // Pay to Witness Script Hash
	ScriptTypeP2TR                   // Pay to Taproot
	ScriptTypeMultisig               // Multisig
	ScriptTypeNullData               // OP_RETURN (null data)
	ScriptTypeNonStandard            // Non-standard script
)

// ScriptInfo represents information about a Bitcoin script
type ScriptInfo struct {
	Type         ScriptType
	Size         int
	OpCodes      []string
	Addresses    []string
	RequiredSigs int
	TotalKeys    int
	IsStandard   bool
	IsSecure     bool
	Warnings     []string
}

// OpCode represents Bitcoin script opcodes
type OpCode struct {
	Name        string
	Value       byte
	Description string
	Deprecated  bool
	Dangerous   bool
}

// NewScriptAnalyzer creates a new script analyzer
func NewScriptAnalyzer() *ScriptAnalyzer {
	return &ScriptAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzeScript analyzes a Bitcoin script for security issues
func (sa *ScriptAnalyzer) AnalyzeScript(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Find script patterns in the content
	scripts := sa.extractScripts(content)

	for _, script := range scripts {
		scriptIssues := sa.analyzeScriptSecurity(script, filePath)
		issues = append(issues, scriptIssues...)
	}

	// Check for script-related security patterns
	issues = append(issues, sa.checkScriptPatterns(filePath, content)...)

	return issues, nil
}

// extractScripts extracts Bitcoin scripts from code content
func (sa *ScriptAnalyzer) extractScripts(content string) []ScriptInfo {
	var scripts []ScriptInfo

	// Pattern for hex-encoded scripts
	hexScriptPattern := regexp.MustCompile(`(?i)([0-9a-f]{20,})`)

	// Pattern for script assembly
	asmScriptPattern := regexp.MustCompile(`(?i)(OP_\w+(?:\s+OP_\w+)*)`)

	// Pattern for script addresses
	addressPattern := regexp.MustCompile(`(?i)(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,62}`)

	lines := strings.Split(content, "\n")
	for _, line := range lines {
		// Check for hex scripts
		if hexMatches := hexScriptPattern.FindAllString(line, -1); len(hexMatches) > 0 {
			for _, hexScript := range hexMatches {
				if len(hexScript) >= 20 { // Minimum script length
					script := sa.parseHexScript(hexScript)
					if script.Type != ScriptTypeUnknown {
						scripts = append(scripts, script)
					}
				}
			}
		}

		// Check for assembly scripts
		if asmMatches := asmScriptPattern.FindAllString(line, -1); len(asmMatches) > 0 {
			for _, asmScript := range asmMatches {
				script := sa.parseAsmScript(asmScript)
				scripts = append(scripts, script)
			}
		}

		// Check for addresses (which imply scripts)
		if addrMatches := addressPattern.FindAllString(line, -1); len(addrMatches) > 0 {
			for _, addr := range addrMatches {
				script := sa.analyzeAddress(addr)
				scripts = append(scripts, script)
			}
		}
	}

	return scripts
}

// parseHexScript parses a hex-encoded script
func (sa *ScriptAnalyzer) parseHexScript(hexScript string) ScriptInfo {
	script := ScriptInfo{
		Type:       ScriptTypeUnknown,
		Size:       len(hexScript) / 2,
		OpCodes:    []string{},
		Addresses:  []string{},
		IsStandard: false,
		IsSecure:   true,
		Warnings:   []string{},
	}

	// Decode hex
	scriptBytes, err := hex.DecodeString(hexScript)
	if err != nil {
		script.Warnings = append(script.Warnings, "Invalid hex encoding")
		return script
	}

	// Analyze script bytes
	script = sa.analyzeScriptBytes(scriptBytes)

	return script
}

// parseAsmScript parses an assembly script
func (sa *ScriptAnalyzer) parseAsmScript(asmScript string) ScriptInfo {
	script := ScriptInfo{
		Type:       ScriptTypeUnknown,
		Size:       len(asmScript),
		OpCodes:    strings.Fields(asmScript),
		Addresses:  []string{},
		IsStandard: false,
		IsSecure:   true,
		Warnings:   []string{},
	}

	// Analyze opcodes
	script = sa.analyzeOpCodes(script.OpCodes)

	return script
}

// analyzeAddress analyzes a Bitcoin address
func (sa *ScriptAnalyzer) analyzeAddress(address string) ScriptInfo {
	script := ScriptInfo{
		Type:       ScriptTypeUnknown,
		Size:       len(address),
		OpCodes:    []string{},
		Addresses:  []string{address},
		IsStandard: true,
		IsSecure:   true,
		Warnings:   []string{},
	}

	// Determine address type
	if strings.HasPrefix(address, "1") {
		script.Type = ScriptTypeP2PKH
	} else if strings.HasPrefix(address, "3") {
		script.Type = ScriptTypeP2SH
	} else if strings.HasPrefix(address, "bc1") {
		if len(address) == 42 {
			script.Type = ScriptTypeP2WPKH
		} else if len(address) == 62 {
			script.Type = ScriptTypeP2WSH
		} else {
			script.Type = ScriptTypeP2TR // Taproot
		}
	}

	return script
}

// analyzeScriptBytes analyzes raw script bytes
func (sa *ScriptAnalyzer) analyzeScriptBytes(scriptBytes []byte) ScriptInfo {
	script := ScriptInfo{
		Type:       ScriptTypeUnknown,
		Size:       len(scriptBytes),
		OpCodes:    []string{},
		Addresses:  []string{},
		IsStandard: false,
		IsSecure:   true,
		Warnings:   []string{},
	}

	// Parse opcodes from bytes
	i := 0
	for i < len(scriptBytes) {
		opcode := scriptBytes[i]
		opcodeInfo := sa.getOpCodeInfo(opcode)

		script.OpCodes = append(script.OpCodes, opcodeInfo.Name)

		// Check for dangerous opcodes
		if opcodeInfo.Dangerous {
			script.IsSecure = false
			script.Warnings = append(script.Warnings, fmt.Sprintf("Dangerous opcode: %s", opcodeInfo.Name))
		}

		// Check for deprecated opcodes
		if opcodeInfo.Deprecated {
			script.Warnings = append(script.Warnings, fmt.Sprintf("Deprecated opcode: %s", opcodeInfo.Name))
		}

		i++

		// Handle push data opcodes
		if opcode >= 1 && opcode <= 75 {
			i += int(opcode) // Skip the data bytes
		} else if opcode == 76 { // OP_PUSHDATA1
			if i < len(scriptBytes) {
				dataLen := int(scriptBytes[i])
				i += 1 + dataLen
			}
		} else if opcode == 77 { // OP_PUSHDATA2
			if i+1 < len(scriptBytes) {
				dataLen := int(scriptBytes[i]) | (int(scriptBytes[i+1]) << 8)
				i += 2 + dataLen
			}
		} else if opcode == 78 { // OP_PUSHDATA4
			if i+3 < len(scriptBytes) {
				dataLen := int(scriptBytes[i]) | (int(scriptBytes[i+1]) << 8) |
					(int(scriptBytes[i+2]) << 16) | (int(scriptBytes[i+3]) << 24)
				i += 4 + dataLen
			}
		}
	}

	// Determine script type based on pattern
	script.Type = sa.determineScriptType(script.OpCodes)
	script.IsStandard = sa.isStandardScript(script.Type, script.OpCodes)

	return script
}

// analyzeOpCodes analyzes a list of opcodes
func (sa *ScriptAnalyzer) analyzeOpCodes(opcodes []string) ScriptInfo {
	script := ScriptInfo{
		Type:       ScriptTypeUnknown,
		Size:       len(opcodes),
		OpCodes:    opcodes,
		Addresses:  []string{},
		IsStandard: false,
		IsSecure:   true,
		Warnings:   []string{},
	}

	// Check each opcode
	for _, opcode := range opcodes {
		opcodeInfo := sa.getOpCodeInfoByName(opcode)

		if opcodeInfo.Dangerous {
			script.IsSecure = false
			script.Warnings = append(script.Warnings, fmt.Sprintf("Dangerous opcode: %s", opcode))
		}

		if opcodeInfo.Deprecated {
			script.Warnings = append(script.Warnings, fmt.Sprintf("Deprecated opcode: %s", opcode))
		}
	}

	// Determine script type
	script.Type = sa.determineScriptType(opcodes)
	script.IsStandard = sa.isStandardScript(script.Type, opcodes)

	// Analyze multisig
	if script.Type == ScriptTypeMultisig {
		script.RequiredSigs, script.TotalKeys = sa.parseMultisig(opcodes)
	}

	return script
}

// determineScriptType determines the script type from opcodes
func (sa *ScriptAnalyzer) determineScriptType(opcodes []string) ScriptType {
	if len(opcodes) == 0 {
		return ScriptTypeUnknown
	}

	// P2PK: <pubkey> OP_CHECKSIG
	if len(opcodes) == 2 && opcodes[1] == "OP_CHECKSIG" {
		return ScriptTypeP2PK
	}

	// P2PKH: OP_DUP OP_HASH160 <pubkeyhash> OP_EQUALVERIFY OP_CHECKSIG
	if len(opcodes) == 5 && opcodes[0] == "OP_DUP" && opcodes[1] == "OP_HASH160" &&
		opcodes[3] == "OP_EQUALVERIFY" && opcodes[4] == "OP_CHECKSIG" {
		return ScriptTypeP2PKH
	}

	// P2SH: OP_HASH160 <scripthash> OP_EQUAL
	if len(opcodes) == 3 && opcodes[0] == "OP_HASH160" && opcodes[2] == "OP_EQUAL" {
		return ScriptTypeP2SH
	}

	// Multisig: OP_M <pubkey1> ... <pubkeyN> OP_N OP_CHECKMULTISIG
	if len(opcodes) >= 4 && strings.HasPrefix(opcodes[0], "OP_") &&
		strings.HasPrefix(opcodes[len(opcodes)-2], "OP_") &&
		opcodes[len(opcodes)-1] == "OP_CHECKMULTISIG" {
		return ScriptTypeMultisig
	}

	// OP_RETURN (null data)
	if len(opcodes) >= 1 && opcodes[0] == "OP_RETURN" {
		return ScriptTypeNullData
	}

	return ScriptTypeNonStandard
}

// isStandardScript checks if a script is standard
func (sa *ScriptAnalyzer) isStandardScript(scriptType ScriptType, opcodes []string) bool {
	switch scriptType {
	case ScriptTypeP2PK, ScriptTypeP2PKH, ScriptTypeP2SH, ScriptTypeP2WPKH, ScriptTypeP2WSH:
		return true
	case ScriptTypeMultisig:
		// Standard multisig: 1-3 required signatures, 1-15 total keys
		m, n := sa.parseMultisig(opcodes)
		return m >= 1 && m <= 3 && n >= 1 && n <= 15 && m <= n
	case ScriptTypeNullData:
		// OP_RETURN with up to 80 bytes of data
		return len(opcodes) <= 2 // OP_RETURN + data
	default:
		return false
	}
}

// parseMultisig parses multisig parameters
func (sa *ScriptAnalyzer) parseMultisig(opcodes []string) (int, int) {
	if len(opcodes) < 4 {
		return 0, 0
	}

	// Extract M (required signatures)
	mStr := strings.TrimPrefix(opcodes[0], "OP_")
	m := sa.opcodeToNumber(mStr)

	// Extract N (total keys)
	nStr := strings.TrimPrefix(opcodes[len(opcodes)-2], "OP_")
	n := sa.opcodeToNumber(nStr)

	return m, n
}

// opcodeToNumber converts opcode string to number
func (sa *ScriptAnalyzer) opcodeToNumber(opcode string) int {
	switch opcode {
	case "1":
		return 1
	case "2":
		return 2
	case "3":
		return 3
	case "4":
		return 4
	case "5":
		return 5
	case "6":
		return 6
	case "7":
		return 7
	case "8":
		return 8
	case "9":
		return 9
	case "10":
		return 10
	case "11":
		return 11
	case "12":
		return 12
	case "13":
		return 13
	case "14":
		return 14
	case "15":
		return 15
	case "16":
		return 16
	default:
		return 0
	}
}

// getOpCodeInfo returns information about an opcode by value
func (sa *ScriptAnalyzer) getOpCodeInfo(value byte) OpCode {
	opcodes := sa.getOpCodeMap()
	for _, opcode := range opcodes {
		if opcode.Value == value {
			return opcode
		}
	}
	return OpCode{Name: fmt.Sprintf("OP_UNKNOWN_%d", value), Value: value}
}

// getOpCodeInfoByName returns information about an opcode by name
func (sa *ScriptAnalyzer) getOpCodeInfoByName(name string) OpCode {
	opcodes := sa.getOpCodeMap()
	for _, opcode := range opcodes {
		if opcode.Name == name {
			return opcode
		}
	}
	return OpCode{Name: name}
}

// getOpCodeMap returns a map of Bitcoin opcodes
func (sa *ScriptAnalyzer) getOpCodeMap() []OpCode {
	return []OpCode{
		{Name: "OP_0", Value: 0x00, Description: "Empty array of bytes is pushed onto the stack"},
		{Name: "OP_1", Value: 0x51, Description: "The number 1 is pushed onto the stack"},
		{Name: "OP_DUP", Value: 0x76, Description: "Duplicates the top stack item"},
		{Name: "OP_HASH160", Value: 0xa9, Description: "The input is hashed with SHA-256 and then with RIPEMD-160"},
		{Name: "OP_EQUAL", Value: 0x87, Description: "Returns 1 if the inputs are exactly equal, 0 otherwise"},
		{Name: "OP_EQUALVERIFY", Value: 0x88, Description: "Same as OP_EQUAL, but runs OP_VERIFY afterward"},
		{Name: "OP_CHECKSIG", Value: 0xac, Description: "The entire transaction's outputs, inputs, and script are hashed"},
		{Name: "OP_CHECKMULTISIG", Value: 0xae, Description: "Compares the first signature against each public key"},
		{Name: "OP_RETURN", Value: 0x6a, Description: "Marks transaction output as invalid"},

		// Deprecated/dangerous opcodes
		{Name: "OP_CAT", Value: 0x7e, Description: "Concatenates two strings", Deprecated: true, Dangerous: true},
		{Name: "OP_SUBSTR", Value: 0x7f, Description: "Returns a section of a string", Deprecated: true, Dangerous: true},
		{Name: "OP_LEFT", Value: 0x80, Description: "Keeps only characters left of the specified point", Deprecated: true, Dangerous: true},
		{Name: "OP_RIGHT", Value: 0x81, Description: "Keeps only characters right of the specified point", Deprecated: true, Dangerous: true},
		{Name: "OP_INVERT", Value: 0x83, Description: "Flips all of the bits in the input", Deprecated: true, Dangerous: true},
		{Name: "OP_AND", Value: 0x84, Description: "Boolean and between each bit in the inputs", Deprecated: true, Dangerous: true},
		{Name: "OP_OR", Value: 0x85, Description: "Boolean or between each bit in the inputs", Deprecated: true, Dangerous: true},
		{Name: "OP_XOR", Value: 0x86, Description: "Boolean exclusive or between each bit in the inputs", Deprecated: true, Dangerous: true},
		{Name: "OP_2MUL", Value: 0x8d, Description: "The input is multiplied by 2", Deprecated: true, Dangerous: true},
		{Name: "OP_2DIV", Value: 0x8e, Description: "The input is divided by 2", Deprecated: true, Dangerous: true},
		{Name: "OP_MUL", Value: 0x95, Description: "a is multiplied by b", Deprecated: true, Dangerous: true},
		{Name: "OP_DIV", Value: 0x96, Description: "a is divided by b", Deprecated: true, Dangerous: true},
		{Name: "OP_MOD", Value: 0x97, Description: "Returns the remainder after dividing a by b", Deprecated: true, Dangerous: true},
		{Name: "OP_LSHIFT", Value: 0x98, Description: "Shifts a left b bits", Deprecated: true, Dangerous: true},
		{Name: "OP_RSHIFT", Value: 0x99, Description: "Shifts a right b bits", Deprecated: true, Dangerous: true},
	}
}

// analyzeScriptSecurity analyzes a script for security issues
func (sa *ScriptAnalyzer) analyzeScriptSecurity(script ScriptInfo, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for non-standard scripts
	if !script.IsStandard {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_script_nonstandard_%d", time.Now().UnixNano()),
			Type:        "non_standard_script",
			Severity:    "medium",
			Title:       "Non-Standard Bitcoin Script",
			Description: "Script does not follow standard Bitcoin script patterns",
			File:        filePath,
			Line:        1,
			Code:        strings.Join(script.OpCodes, " "),
			Chain:       "bitcoin",
			Category:    "smart_contract",
			Suggestion:  "Use standard script types (P2PKH, P2SH, P2WPKH, P2WSH) for better compatibility",
			References:  []string{"https://bitcoin.org/en/developer-guide#standard-transactions"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for insecure scripts
	if !script.IsSecure {
		for _, warning := range script.Warnings {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_script_insecure_%d", time.Now().UnixNano()),
				Type:        "insecure_script",
				Severity:    "high",
				Title:       "Insecure Bitcoin Script",
				Description: warning,
				File:        filePath,
				Line:        1,
				Code:        strings.Join(script.OpCodes, " "),
				Chain:       "bitcoin",
				Category:    "smart_contract",
				Suggestion:  "Avoid using deprecated or dangerous opcodes",
				References:  []string{"https://en.bitcoin.it/wiki/Script#Disabled_opcodes"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	// Check multisig security
	if script.Type == ScriptTypeMultisig {
		issues = append(issues, sa.checkMultisigSecurity(script, filePath)...)
	}

	return issues
}

// checkMultisigSecurity checks multisig script security
func (sa *ScriptAnalyzer) checkMultisigSecurity(script ScriptInfo, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for 1-of-N multisig (provides no additional security)
	if script.RequiredSigs == 1 && script.TotalKeys > 1 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_multisig_1ofn_%d", time.Now().UnixNano()),
			Type:        "weak_multisig",
			Severity:    "medium",
			Title:       "Weak Multisig Configuration",
			Description: fmt.Sprintf("1-of-%d multisig provides no additional security over single signature", script.TotalKeys),
			File:        filePath,
			Line:        1,
			Code:        strings.Join(script.OpCodes, " "),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Use at least 2-of-N multisig for meaningful security improvement",
			References:  []string{"https://bitcoin.org/en/developer-guide#multisig"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for excessive multisig (too many keys)
	if script.TotalKeys > 15 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_multisig_excessive_%d", time.Now().UnixNano()),
			Type:        "excessive_multisig",
			Severity:    "low",
			Title:       "Excessive Multisig Keys",
			Description: fmt.Sprintf("Multisig with %d keys exceeds standard limit of 15", script.TotalKeys),
			File:        filePath,
			Line:        1,
			Code:        strings.Join(script.OpCodes, " "),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Limit multisig to 15 keys maximum for standard compliance",
			References:  []string{"https://bitcoin.org/en/developer-guide#multisig"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// checkScriptPatterns checks for script-related security patterns in code
func (sa *ScriptAnalyzer) checkScriptPatterns(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	// Check for hardcoded scripts
	hardcodedScriptPattern := regexp.MustCompile(`(?i)(script|scriptPubKey|scriptSig)\s*[:=]\s*["'][0-9a-fA-F]{20,}["']`)

	for i, line := range lines {
		if hardcodedScriptPattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_hardcoded_script_%d", i+1),
				Type:        "hardcoded_script",
				Severity:    "medium",
				Title:       "Hardcoded Bitcoin Script",
				Description: "Bitcoin script is hardcoded in source code",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "bitcoin",
				Category:    "smart_contract",
				Suggestion:  "Generate scripts dynamically or store them in configuration",
				References:  []string{"https://bitcoin.org/en/developer-guide#transactions"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}
