import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RouterModule } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <h1>🛡️ Security Dashboard</h1>
      
      <!-- Stats Cards -->
      <div class="stats-grid">
        <mat-card class="stat-card">
          <mat-card-header>
            <mat-icon>security</mat-icon>
            <mat-card-title>Total Scans</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ stats.totalScans }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card critical">
          <mat-card-header>
            <mat-icon>error</mat-icon>
            <mat-card-title>Critical Issues</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ stats.criticalIssues }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card high">
          <mat-card-header>
            <mat-icon>warning</mat-icon>
            <mat-card-title>High Issues</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ stats.highIssues }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card medium">
          <mat-card-header>
            <mat-icon>info</mat-icon>
            <mat-card-title>Medium Issues</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ stats.mediumIssues }}</div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Recent Scans -->
      <mat-card class="recent-scans-card">
        <mat-card-header>
          <mat-card-title>Recent Scans</mat-card-title>
          <button mat-button routerLink="/scan" color="primary">
            <mat-icon>add</mat-icon>
            New Scan
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="recentScans" class="scans-table">
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>Scan ID</th>
                <td mat-cell *matCellDef="let scan">{{ scan.id }}</td>
              </ng-container>

              <ng-container matColumnDef="project">
                <th mat-header-cell *matHeaderCellDef>Project</th>
                <td mat-cell *matCellDef="let scan">{{ getProjectName(scan.project_path) }}</td>
              </ng-container>

              <ng-container matColumnDef="chains">
                <th mat-header-cell *matHeaderCellDef>Chains</th>
                <td mat-cell *matCellDef="let scan">
                  <mat-chip-listbox>
                    <mat-chip *ngFor="let chain of scan.chains">{{ chain }}</mat-chip>
                  </mat-chip-listbox>
                </td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let scan">
                  <mat-chip [class]="'status-' + scan.status">{{ scan.status }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="issues">
                <th mat-header-cell *matHeaderCellDef>Issues</th>
                <td mat-cell *matCellDef="let scan">{{ scan.issues?.length || 0 }}</td>
              </ng-container>

              <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef>Date</th>
                <td mat-cell *matCellDef="let scan">{{ formatDate(scan.created_at) }}</td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let scan">
                  <button mat-icon-button [routerLink]="['/scan', scan.id]">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Top Issues -->
      <mat-card class="top-issues-card">
        <mat-card-header>
          <mat-card-title>Top Security Issues</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="issues-list">
            <div *ngFor="let issue of topIssues" class="issue-item">
              <div class="issue-severity">
                <mat-chip [style.background-color]="getSeverityColor(issue.severity)">
                  {{ issue.severity }}
                </mat-chip>
              </div>
              <div class="issue-details">
                <h4>{{ issue.title }}</h4>
                <p>{{ issue.description }}</p>
                <small>{{ issue.file }}:{{ issue.line }}</small>
              </div>
              <div class="issue-chain">
                <mat-chip>{{ issue.chain }}</mat-chip>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions -->
      <mat-card class="quick-actions-card">
        <mat-card-header>
          <mat-card-title>Quick Actions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button color="primary" routerLink="/scan">
              <mat-icon>security</mat-icon>
              Start New Scan
            </button>
            <button mat-raised-button color="accent" routerLink="/checklist">
              <mat-icon>checklist</mat-icon>
              Security Checklist
            </button>
            <button mat-raised-button routerLink="/reports">
              <mat-icon>assessment</mat-icon>
              Generate Report
            </button>
            <button mat-raised-button routerLink="/settings">
              <mat-icon>settings</mat-icon>
              Settings
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      margin-bottom: 30px;
      color: #333;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      text-align: center;
    }

    .stat-card mat-card-header {
      justify-content: center;
      margin-bottom: 10px;
    }

    .stat-card mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }

    .stat-number {
      font-size: 48px;
      font-weight: bold;
      color: #333;
    }

    .stat-card.critical .stat-number {
      color: #f44336;
    }

    .stat-card.high .stat-number {
      color: #ff9800;
    }

    .stat-card.medium .stat-number {
      color: #ffeb3b;
    }

    .recent-scans-card,
    .top-issues-card,
    .quick-actions-card {
      margin-bottom: 30px;
    }

    .recent-scans-card mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-container {
      overflow-x: auto;
    }

    .scans-table {
      width: 100%;
    }

    .status-completed {
      background-color: #4caf50;
      color: white;
    }

    .status-running {
      background-color: #2196f3;
      color: white;
    }

    .status-failed {
      background-color: #f44336;
      color: white;
    }

    .status-pending {
      background-color: #ff9800;
      color: white;
    }

    .issues-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .issue-item {
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .issue-details {
      flex: 1;
    }

    .issue-details h4 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .issue-details p {
      margin: 0 0 5px 0;
      color: #666;
      font-size: 14px;
    }

    .issue-details small {
      color: #999;
      font-size: 12px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .actions-grid button {
      height: 60px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .actions-grid mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  `]
})
export class DashboardComponent implements OnInit {
  stats = {
    totalScans: 0,
    criticalIssues: 0,
    highIssues: 0,
    mediumIssues: 0
  };

  recentScans: ScanResult[] = [];
  topIssues: SecurityIssue[] = [];
  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Load recent scans
    this.apiService.getScanHistory().subscribe({
      next: (response) => {
        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent
        this.calculateStats(response.scans);
      },
      error: (error) => {
        console.error('Error loading scan history:', error);
        // Use mock data for development
        this.loadMockData();
      }
    });
  }

  loadMockData(): void {
    // Generate mock data for development
    const mockScan = this.apiService.generateMockScanResult();
    this.recentScans = [mockScan];
    this.topIssues = mockScan.issues;
    this.stats = {
      totalScans: 1,
      criticalIssues: mockScan.severity_counts['critical'] || 0,
      highIssues: mockScan.severity_counts['high'] || 0,
      mediumIssues: mockScan.severity_counts['medium'] || 0
    };
  }

  calculateStats(scans: ScanResult[]): void {
    this.stats.totalScans = scans.length;
    
    let criticalTotal = 0;
    let highTotal = 0;
    let mediumTotal = 0;
    let allIssues: SecurityIssue[] = [];

    scans.forEach(scan => {
      criticalTotal += scan.severity_counts?.['critical'] || 0;
      highTotal += scan.severity_counts?.['high'] || 0;
      mediumTotal += scan.severity_counts?.['medium'] || 0;
      allIssues = allIssues.concat(scan.issues || []);
    });

    this.stats.criticalIssues = criticalTotal;
    this.stats.highIssues = highTotal;
    this.stats.mediumIssues = mediumTotal;

    // Get top 5 most severe issues
    this.topIssues = allIssues
      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))
      .slice(0, 5);
  }

  getSeverityWeight(severity: string): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
    return weights[severity as keyof typeof weights] || 0;
  }

  getSeverityColor(severity: string): string {
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}
