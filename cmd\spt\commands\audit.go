package commands

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/pkg/bitcoin"
	"blockchain-spt/spt/backend/pkg/ethereum"

	"blockchain-spt/spt/backend/internal/config"

	"github.com/spf13/cobra"
)

// AuditOptions represents audit command options
type AuditOptions struct {
	Path           string
	Chain          string
	Depth          string
	IncludeTests   bool
	GenerateReport bool
	ReportPath     string
	Interactive    bool
	FixSuggestions bool
}

// NewAuditCommand creates the audit command
func NewAuditCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "audit",
		Short: "Perform comprehensive security audits",
		Long: `Perform comprehensive security audits on blockchain applications.

The audit command provides deep security analysis with detailed reporting
and remediation suggestions for smart contracts and blockchain applications.`,
	}

	// Add subcommands
	cmd.AddCommand(newAuditEthereumCommand())
	cmd.AddCommand(newAuditBitcoinCommand())
	cmd.AddCommand(newAuditContractsCommand())

	return cmd
}

// newAuditEthereumCommand creates the ethereum audit subcommand
func newAuditEthereumCommand() *cobra.Command {
	opts := &AuditOptions{}

	cmd := &cobra.Command{
		Use:   "ethereum [path]",
		Short: "Audit Ethereum smart contracts and DApps",
		Long: `Perform comprehensive security audit of Ethereum smart contracts and DApps.

This command analyzes:
- Smart contract vulnerabilities (reentrancy, overflow, etc.)
- Gas optimization opportunities
- Access control issues
- External call security
- State variable security
- Event and modifier usage

Examples:
  spt audit ethereum ./contracts              # Audit all contracts
  spt audit ethereum --depth deep ./src      # Deep analysis
  spt audit ethereum --interactive ./token   # Interactive audit`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			opts.Chain = "ethereum"
			return runAudit(cmd, opts)
		},
	}

	addAuditFlags(cmd, opts)
	return cmd
}

// newAuditBitcoinCommand creates the bitcoin audit subcommand
func newAuditBitcoinCommand() *cobra.Command {
	opts := &AuditOptions{}

	cmd := &cobra.Command{
		Use:   "bitcoin [path]",
		Short: "Audit Bitcoin scripts and wallet implementations",
		Long: `Perform comprehensive security audit of Bitcoin scripts and wallet implementations.

This command analyzes:
- Bitcoin script security
- UTXO management patterns
- Wallet security practices
- Private key handling
- Transaction security
- Multisig configurations

Examples:
  spt audit bitcoin ./wallet                 # Audit wallet implementation
  spt audit bitcoin --depth deep ./scripts  # Deep script analysis
  spt audit bitcoin --interactive ./btc     # Interactive audit`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			opts.Chain = "bitcoin"
			return runAudit(cmd, opts)
		},
	}

	addAuditFlags(cmd, opts)
	return cmd
}

// newAuditContractsCommand creates the contracts audit subcommand
func newAuditContractsCommand() *cobra.Command {
	opts := &AuditOptions{}

	cmd := &cobra.Command{
		Use:   "contracts [path]",
		Short: "Audit smart contracts (all chains)",
		Long: `Perform comprehensive security audit of smart contracts across all supported blockchains.

This command analyzes contracts for:
- Common vulnerability patterns
- Best practice violations
- Security anti-patterns
- Gas optimization opportunities
- Access control issues

Examples:
  spt audit contracts ./contracts            # Audit all contracts
  spt audit contracts --chain ethereum ./src # Ethereum only
  spt audit contracts --generate-report ./   # Generate detailed report`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runAudit(cmd, opts)
		},
	}

	addAuditFlags(cmd, opts)
	cmd.Flags().StringVar(&opts.Chain, "chain", "", "Specific blockchain to audit (ethereum, bitcoin)")

	return cmd
}

// addAuditFlags adds common audit flags to a command
func addAuditFlags(cmd *cobra.Command, opts *AuditOptions) {
	cmd.Flags().StringVar(&opts.Depth, "depth", "standard", "Audit depth (quick, standard, deep)")
	cmd.Flags().BoolVar(&opts.IncludeTests, "include-tests", false, "Include test files in audit")
	cmd.Flags().BoolVar(&opts.GenerateReport, "generate-report", false, "Generate detailed audit report")
	cmd.Flags().StringVar(&opts.ReportPath, "report-path", "", "Path for audit report (default: audit-report.html)")
	cmd.Flags().BoolVar(&opts.Interactive, "interactive", false, "Interactive audit mode")
	cmd.Flags().BoolVar(&opts.FixSuggestions, "fix-suggestions", true, "Include fix suggestions in output")
}

// runAudit executes the audit command
func runAudit(cmd *cobra.Command, opts *AuditOptions) error {
	// Load configuration
	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Set audit-specific configuration
	cfg.Security.Level = "strict"

	// Validate path
	if _, err := os.Stat(opts.Path); os.IsNotExist(err) {
		return fmt.Errorf("path does not exist: %s", opts.Path)
	}

	fmt.Printf("🔍 Starting security audit of: %s\n", opts.Path)
	fmt.Printf("📊 Audit configuration:\n")
	fmt.Printf("   - Chain: %s\n", getChainDisplay(opts.Chain))
	fmt.Printf("   - Depth: %s\n", opts.Depth)
	fmt.Printf("   - Include tests: %v\n", opts.IncludeTests)
	fmt.Printf("   - Interactive: %v\n", opts.Interactive)
	fmt.Printf("\n")

	startTime := time.Now()

	// Perform audit based on chain
	var issues []models.SecurityIssue
	var auditErr error

	switch strings.ToLower(opts.Chain) {
	case "ethereum":
		issues, auditErr = auditEthereum(cfg, opts)
	case "bitcoin":
		issues, auditErr = auditBitcoin(cfg, opts)
	case "":
		// Audit all supported chains
		ethIssues, ethErr := auditEthereum(cfg, opts)
		if ethErr != nil {
			fmt.Printf("⚠️  Ethereum audit warning: %v\n", ethErr)
		} else {
			issues = append(issues, ethIssues...)
		}

		btcIssues, btcErr := auditBitcoin(cfg, opts)
		if btcErr != nil {
			fmt.Printf("⚠️  Bitcoin audit warning: %v\n", btcErr)
		} else {
			issues = append(issues, btcIssues...)
		}
	default:
		return fmt.Errorf("unsupported chain: %s", opts.Chain)
	}

	if auditErr != nil {
		return fmt.Errorf("audit failed: %w", auditErr)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ Audit completed in %v\n", duration)

	// Display results
	displayOptions := &ScanOptions{
		Format:   "table",
		Severity: "",
	}

	if err := displayResults(issues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Generate detailed report if requested
	if opts.GenerateReport {
		reportPath := opts.ReportPath
		if reportPath == "" {
			reportPath = fmt.Sprintf("audit-report-%s.html", time.Now().Format("2006-01-02-15-04-05"))
		}

		if err := generateAuditReport(issues, opts, reportPath); err != nil {
			return fmt.Errorf("failed to generate report: %w", err)
		}

		fmt.Printf("📄 Detailed audit report saved to: %s\n", reportPath)
	}

	// Interactive mode
	if opts.Interactive {
		return runInteractiveAudit(issues, opts)
	}

	// Display audit summary
	displayAuditSummary(issues, opts)

	return nil
}

// auditEthereum performs Ethereum-specific audit
func auditEthereum(cfg *config.Config, opts *AuditOptions) ([]models.SecurityIssue, error) {
	fmt.Printf("🔎 Running Ethereum security audit...\n")

	scanner, err := ethereum.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Ethereum scanner: %w", err)
	}

	ctx := context.Background()

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return nil, fmt.Errorf("failed to stat path: %w", err)
	}

	var issues []models.SecurityIssue
	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return nil, err
	}

	fmt.Printf("   Found %d Ethereum-related issues\n", len(issues))
	return issues, nil
}

// auditBitcoin performs Bitcoin-specific audit
func auditBitcoin(cfg *config.Config, opts *AuditOptions) ([]models.SecurityIssue, error) {
	fmt.Printf("🔎 Running Bitcoin security audit...\n")

	scanner, err := bitcoin.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Bitcoin scanner: %w", err)
	}

	ctx := context.Background()

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return nil, fmt.Errorf("failed to stat path: %w", err)
	}

	var issues []models.SecurityIssue
	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return nil, err
	}

	fmt.Printf("   Found %d Bitcoin-related issues\n", len(issues))
	return issues, nil
}

// generateAuditReport generates a detailed HTML audit report
func generateAuditReport(issues []models.SecurityIssue, opts *AuditOptions, reportPath string) error {
	// Create HTML report
	html := generateHTMLReport(issues, opts)

	return os.WriteFile(reportPath, []byte(html), 0644)
}

// generateHTMLReport creates an HTML audit report
func generateHTMLReport(issues []models.SecurityIssue, opts *AuditOptions) string {
	// This would generate a comprehensive HTML report
	// For now, return a basic template
	return fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>Security Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .issue { margin: 20px 0; padding: 15px; border-left: 4px solid #ccc; }
        .critical { border-left-color: #d32f2f; }
        .high { border-left-color: #f57c00; }
        .medium { border-left-color: #fbc02d; }
        .low { border-left-color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Security Audit Report</h1>
        <p>Generated: %s</p>
        <p>Path: %s</p>
        <p>Total Issues: %d</p>
    </div>
    
    <h2>Issues Found</h2>
    %s
</body>
</html>`, time.Now().Format("2006-01-02 15:04:05"), opts.Path, len(issues), generateIssuesHTML(issues))
}

// generateIssuesHTML generates HTML for issues
func generateIssuesHTML(issues []models.SecurityIssue) string {
	var html strings.Builder

	for _, issue := range issues {
		severity := strings.ToLower(issue.Severity)
		html.WriteString(fmt.Sprintf(`
    <div class="issue %s">
        <h3>%s [%s]</h3>
        <p><strong>File:</strong> %s:%d</p>
        <p><strong>Type:</strong> %s</p>
        <p><strong>Description:</strong> %s</p>
        <p><strong>Suggestion:</strong> %s</p>
    </div>`, severity, issue.Title, strings.ToUpper(issue.Severity),
			issue.File, issue.Line, issue.Type, issue.Description, issue.Suggestion))
	}

	return html.String()
}

// runInteractiveAudit runs interactive audit mode
func runInteractiveAudit(issues []models.SecurityIssue, opts *AuditOptions) error {
	fmt.Printf("\n🔄 Interactive Audit Mode\n")
	fmt.Printf("═══════════════════════════\n")

	// This would implement interactive features like:
	// - Step through issues one by one
	// - Show detailed explanations
	// - Provide fix suggestions
	// - Allow marking issues as false positives

	fmt.Printf("Interactive mode not yet implemented\n")
	return nil
}

// displayAuditSummary displays audit-specific summary information
func displayAuditSummary(issues []models.SecurityIssue, opts *AuditOptions) {
	fmt.Printf("\n📊 Audit Summary\n")
	fmt.Printf("═══════════════\n")

	if len(issues) == 0 {
		fmt.Printf("🎉 No security issues found! Your code looks secure.\n")
		return
	}

	// Count critical and high severity issues
	critical := 0
	high := 0

	for _, issue := range issues {
		switch strings.ToLower(issue.Severity) {
		case "critical":
			critical++
		case "high":
			high++
		}
	}

	if critical > 0 {
		fmt.Printf("🚨 CRITICAL: %d critical security issues require immediate attention\n", critical)
	}

	if high > 0 {
		fmt.Printf("⚠️  HIGH: %d high severity issues should be addressed soon\n", high)
	}

	fmt.Printf("\n💡 Recommendations:\n")
	if critical > 0 {
		fmt.Printf("   1. Address all critical issues before deployment\n")
	}
	if high > 0 {
		fmt.Printf("   2. Review and fix high severity issues\n")
	}
	fmt.Printf("   3. Consider implementing additional security measures\n")
	fmt.Printf("   4. Regular security audits are recommended\n")
}
