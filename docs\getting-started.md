# Getting Started with SPT

## Overview

The Blockchain Security Protocol Tool (SPT) is a comprehensive security solution for Ethereum and Bitcoin blockchain development. This guide will help you get started with SPT quickly.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Go 1.21+** - For the backend services
- **Node.js 18+** - For the frontend dashboard
- **Angular CLI** - For frontend development
- **Git** - For version control

## Installation

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd Blockchain.SPT
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install Go dependencies
go mod tidy

# Run the backend server
go run cmd/main.go
```

The backend server will start on `http://localhost:8080`

### 3. CLI Tool Setup

```bash
# Navigate to CLI directory
cd cli

# Build the CLI tool
go build -o spt

# Make it executable (Linux/Mac)
chmod +x spt

# Add to PATH (optional)
sudo mv spt /usr/local/bin/
```

### 4. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start the development server
npm start
```

The frontend will be available at `http://localhost:4200`

## Quick Start

### 1. Run Your First Scan

Using the CLI:
```bash
# Scan current directory for Ethereum and Bitcoin issues
spt scan --chain ethereum,bitcoin

# Scan specific project path
spt scan --path /path/to/project --chain ethereum

# Quick key check
spt check-keys
```

Using the Web Interface:
1. Open `http://localhost:4200` in your browser
2. Navigate to "Security Scan"
3. Enter your project path
4. Select blockchain chains to scan
5. Click "Start Scan"

### 2. View Results

- **CLI**: Results are displayed in the terminal
- **Web**: View results in the dashboard or scan details page
- **Export**: Generate reports in markdown, JSON, or PDF format

### 3. Security Checklist

Access the interactive security checklist:
- Web: Navigate to "Security Checklist"
- CLI: `spt checklist`

## Configuration

### Project Configuration

Create a `spt.config.json` file in your project root:

```json
{
  "chains": ["ethereum", "bitcoin"],
  "scanPaths": ["./contracts", "./src"],
  "excludePaths": ["./node_modules", "./build"],
  "securityLevel": "strict"
}
```

### Environment Variables

Set these environment variables for advanced configuration:

```bash
export SPT_LOG_LEVEL=info
export SPT_DB_TYPE=sqlite
export SPT_SERVER_PORT=8080
```

## Common Use Cases

### 1. Smart Contract Security Audit

```bash
# Audit Solidity contracts
spt audit contracts/ --chain ethereum

# Check for specific vulnerabilities
spt scan --chain ethereum --check-reentrancy --check-overflow
```

### 2. Wallet Security Check

```bash
# Check for exposed private keys
spt check-keys

# Scan for wallet-related issues
spt scan --chain bitcoin --wallet-security
```

### 3. Environment Security

```bash
# Check environment configuration
spt secure-env

# Scan CI/CD files
spt scan --environment-check
```

### 4. Continuous Integration

Add SPT to your CI/CD pipeline:

```yaml
# GitHub Actions example
- name: Security Scan
  run: |
    spt scan --chain ethereum,bitcoin
    spt check-keys
    spt secure-env
```

## Troubleshooting

### Common Issues

1. **Backend not starting**
   - Check if port 8080 is available
   - Verify Go installation: `go version`
   - Check logs for detailed error messages

2. **Frontend build errors**
   - Ensure Node.js 18+ is installed
   - Clear npm cache: `npm cache clean --force`
   - Delete node_modules and reinstall: `rm -rf node_modules && npm install`

3. **CLI tool not found**
   - Ensure the binary is built: `go build -o spt`
   - Check if it's in your PATH
   - Use full path to executable: `./spt`

### Getting Help

- Check the [API Documentation](api.md)
- Review [Security Best Practices](security-practices.md)
- Open an issue on GitHub
- Join our community discussions

## Next Steps

1. **Explore Advanced Features**
   - Custom security rules
   - Plugin development
   - Report customization

2. **Integrate with Your Workflow**
   - Set up automated scans
   - Configure notifications
   - Create custom checklists

3. **Stay Updated**
   - Follow security best practices
   - Update SPT regularly
   - Monitor for new vulnerability patterns

## Support

For additional support:
- Documentation: [docs/](.)
- Issues: GitHub Issues
- Community: GitHub Discussions
- Email: <EMAIL>
