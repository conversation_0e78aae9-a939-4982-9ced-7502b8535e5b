package ethereum

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// ASTNode represents a node in the Solidity AST
type ASTNode struct {
	ID               int                    `json:"id"`
	NodeType         string                 `json:"nodeType"`
	Src              string                 `json:"src"`
	Name             string                 `json:"name,omitempty"`
	TypeName         *ASTNode               `json:"typeName,omitempty"`
	Body             *ASTNode               `json:"body,omitempty"`
	Statements       []ASTNode              `json:"statements,omitempty"`
	Expression       *ASTNode               `json:"expression,omitempty"`
	Left             *ASTNode               `json:"left,omitempty"`
	Right            *ASTNode               `json:"right,omitempty"`
	Arguments        []ASTNode              `json:"arguments,omitempty"`
	Parameters       *ASTNode               `json:"parameters,omitempty"`
	ReturnParameters *ASTNode               `json:"returnParameters,omitempty"`
	Modifiers        []ASTNode              `json:"modifiers,omitempty"`
	Visibility       string                 `json:"visibility,omitempty"`
	StateMutability  string                 `json:"stateMutability,omitempty"`
	Kind             string                 `json:"kind,omitempty"`
	Operator         string                 `json:"operator,omitempty"`
	MemberName       string                 `json:"memberName,omitempty"`
	Children         []ASTNode              `json:"nodes,omitempty"`
	Attributes       map[string]interface{} `json:"attributes,omitempty"`
}

// ContractAST represents the parsed AST of a Solidity contract
type ContractAST struct {
	SourceUnit ASTNode
	Contracts  []ContractInfo
	Functions  []FunctionInfo
	StateVars  []StateVarInfo
	Events     []EventInfo
	Modifiers  []ModifierInfo
	Imports    []ImportInfo
	Pragmas    []PragmaInfo
}

// ContractInfo represents contract-level information
type ContractInfo struct {
	Name          string
	Kind          string // contract, interface, library
	BaseContracts []string
	Functions     []string
	Events        []string
	Modifiers     []string
	StateVars     []string
	Inheritance   []string
	Location      SourceLocation
}

// FunctionInfo represents function-level information
type FunctionInfo struct {
	Name            string
	Visibility      string
	StateMutability string
	Parameters      []ParameterInfo
	Returns         []ParameterInfo
	Modifiers       []string
	Body            *ASTNode
	Location        SourceLocation
	IsConstructor   bool
	IsReceive       bool
	IsFallback      bool
}

// StateVarInfo represents state variable information
type StateVarInfo struct {
	Name       string
	Type       string
	Visibility string
	Constant   bool
	Immutable  bool
	Location   SourceLocation
}

// EventInfo represents event information
type EventInfo struct {
	Name       string
	Parameters []ParameterInfo
	Anonymous  bool
	Location   SourceLocation
}

// ModifierInfo represents modifier information
type ModifierInfo struct {
	Name       string
	Parameters []ParameterInfo
	Body       *ASTNode
	Location   SourceLocation
}

// ParameterInfo represents parameter information
type ParameterInfo struct {
	Name string
	Type string
}

// ImportInfo represents import information
type ImportInfo struct {
	Path     string
	Symbols  []string
	Location SourceLocation
}

// PragmaInfo represents pragma information
type PragmaInfo struct {
	Name     string
	Value    string
	Location SourceLocation
}

// SourceLocation represents source code location
type SourceLocation struct {
	Start  int
	Length int
	Line   int
	Column int
}

// ASTParser handles Solidity AST parsing and analysis
type ASTParser struct {
	logger *logrus.Logger
}

// NewASTParser creates a new AST parser instance
func NewASTParser() *ASTParser {
	return &ASTParser{
		logger: logrus.New(),
	}
}

// ParseContract parses a Solidity contract and returns its AST
func (p *ASTParser) ParseContract(filePath string) (*ContractAST, error) {
	// First try to use solc compiler for AST generation
	ast, err := p.parseWithSolc(filePath)
	if err != nil {
		p.logger.Warnf("Failed to parse with solc, falling back to regex parsing: %v", err)
		// Fallback to regex-based parsing
		return p.parseWithRegex(filePath)
	}
	return ast, nil
}

// parseWithSolc uses the Solidity compiler to generate AST
func (p *ASTParser) parseWithSolc(filePath string) (*ContractAST, error) {
	// Check if solc is available
	if !p.isSolcAvailable() {
		return nil, fmt.Errorf("solc compiler not available")
	}

	// Generate AST using solc
	cmd := exec.Command("solc", "--ast-compact-json", filePath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run solc: %w", err)
	}

	// Parse the JSON output
	var astData map[string]interface{}
	if err := json.Unmarshal(output, &astData); err != nil {
		return nil, fmt.Errorf("failed to parse AST JSON: %w", err)
	}

	// Convert to our AST structure
	return p.convertSolcAST(astData)
}

// parseWithRegex uses regex-based parsing as fallback
func (p *ASTParser) parseWithRegex(filePath string) (*ContractAST, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	ast := &ContractAST{
		Contracts: []ContractInfo{},
		Functions: []FunctionInfo{},
		StateVars: []StateVarInfo{},
		Events:    []EventInfo{},
		Modifiers: []ModifierInfo{},
		Imports:   []ImportInfo{},
		Pragmas:   []PragmaInfo{},
	}

	source := string(content)
	lines := strings.Split(source, "\n")

	// Parse pragmas
	ast.Pragmas = p.parsePragmas(source, lines)

	// Parse imports
	ast.Imports = p.parseImports(source, lines)

	// Parse contracts
	ast.Contracts = p.parseContracts(source, lines)

	// Parse functions
	ast.Functions = p.parseFunctions(source, lines)

	// Parse state variables
	ast.StateVars = p.parseStateVariables(source, lines)

	// Parse events
	ast.Events = p.parseEvents(source, lines)

	// Parse modifiers
	ast.Modifiers = p.parseModifiers(source, lines)

	return ast, nil
}

// parsePragmas extracts pragma directives
func (p *ASTParser) parsePragmas(source string, lines []string) []PragmaInfo {
	var pragmas []PragmaInfo
	pragmaRegex := regexp.MustCompile(`pragma\s+(\w+)\s+([^;]+);`)

	for i, line := range lines {
		matches := pragmaRegex.FindStringSubmatch(line)
		if len(matches) == 3 {
			pragmas = append(pragmas, PragmaInfo{
				Name:  matches[1],
				Value: strings.TrimSpace(matches[2]),
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, "pragma"),
				},
			})
		}
	}

	return pragmas
}

// parseImports extracts import statements
func (p *ASTParser) parseImports(source string, lines []string) []ImportInfo {
	var imports []ImportInfo
	importRegex := regexp.MustCompile(`import\s+(?:"([^"]+)"|'([^']+)'|\{([^}]+)\}\s+from\s+(?:"([^"]+)"|'([^']+)'))\s*;`)

	for i, line := range lines {
		matches := importRegex.FindStringSubmatch(line)
		if len(matches) > 0 {
			var path string
			var symbols []string

			if matches[1] != "" || matches[2] != "" {
				// Simple import
				path = matches[1]
				if path == "" {
					path = matches[2]
				}
			} else if matches[3] != "" {
				// Named imports
				symbolsStr := matches[3]
				symbols = strings.Split(symbolsStr, ",")
				for j := range symbols {
					symbols[j] = strings.TrimSpace(symbols[j])
				}
				path = matches[4]
				if path == "" {
					path = matches[5]
				}
			}

			imports = append(imports, ImportInfo{
				Path:    path,
				Symbols: symbols,
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, "import"),
				},
			})
		}
	}

	return imports
}

// parseContracts extracts contract definitions
func (p *ASTParser) parseContracts(source string, lines []string) []ContractInfo {
	var contracts []ContractInfo
	contractRegex := regexp.MustCompile(`(contract|interface|library)\s+(\w+)(?:\s+is\s+([^{]+))?\s*\{`)

	for i, line := range lines {
		matches := contractRegex.FindStringSubmatch(line)
		if len(matches) >= 3 {
			var inheritance []string
			if matches[3] != "" {
				inheritance = strings.Split(matches[3], ",")
				for j := range inheritance {
					inheritance[j] = strings.TrimSpace(inheritance[j])
				}
			}

			contracts = append(contracts, ContractInfo{
				Name:        matches[2],
				Kind:        matches[1],
				Inheritance: inheritance,
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, matches[1]),
				},
			})
		}
	}

	return contracts
}

// parseFunctions extracts function definitions
func (p *ASTParser) parseFunctions(source string, lines []string) []FunctionInfo {
	var functions []FunctionInfo
	functionRegex := regexp.MustCompile(`function\s+(\w+)?\s*\(([^)]*)\)\s*(.*?)\s*(?:returns\s*\(([^)]*)\))?\s*(?:\{|;)`)

	for i, line := range lines {
		matches := functionRegex.FindStringSubmatch(line)
		if len(matches) >= 4 {
			name := matches[1]
			if name == "" {
				// Check for constructor, receive, or fallback
				if strings.Contains(line, "constructor") {
					name = "constructor"
				} else if strings.Contains(line, "receive") {
					name = "receive"
				} else if strings.Contains(line, "fallback") {
					name = "fallback"
				}
			}

			// Parse parameters
			params := p.parseParameters(matches[2])

			// Parse return parameters
			var returns []ParameterInfo
			if len(matches) > 4 && matches[4] != "" {
				returns = p.parseParameters(matches[4])
			}

			// Parse modifiers and visibility
			modifiersStr := matches[3]
			visibility, stateMutability, modifiers := p.parseModifiersAndVisibility(modifiersStr)

			functions = append(functions, FunctionInfo{
				Name:            name,
				Visibility:      visibility,
				StateMutability: stateMutability,
				Parameters:      params,
				Returns:         returns,
				Modifiers:       modifiers,
				IsConstructor:   name == "constructor",
				IsReceive:       name == "receive",
				IsFallback:      name == "fallback",
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, "function"),
				},
			})
		}
	}

	return functions
}

// parseStateVariables extracts state variable definitions
func (p *ASTParser) parseStateVariables(source string, lines []string) []StateVarInfo {
	var stateVars []StateVarInfo
	stateVarRegex := regexp.MustCompile(`^\s*([^=\s]+)\s+(public|private|internal)?\s*(constant|immutable)?\s+(\w+)(?:\s*=\s*[^;]+)?\s*;`)

	for i, line := range lines {
		// Skip function lines and other non-state variable lines
		if strings.Contains(line, "function") || strings.Contains(line, "event") ||
			strings.Contains(line, "modifier") || strings.Contains(line, "//") ||
			strings.TrimSpace(line) == "" {
			continue
		}

		matches := stateVarRegex.FindStringSubmatch(line)
		if len(matches) >= 5 {
			visibility := "internal" // default
			if matches[2] != "" {
				visibility = matches[2]
			}

			constant := matches[3] == "constant"
			immutable := matches[3] == "immutable"

			stateVars = append(stateVars, StateVarInfo{
				Name:       matches[4],
				Type:       matches[1],
				Visibility: visibility,
				Constant:   constant,
				Immutable:  immutable,
				Location: SourceLocation{
					Line:   i + 1,
					Column: 0,
				},
			})
		}
	}

	return stateVars
}

// parseEvents extracts event definitions
func (p *ASTParser) parseEvents(source string, lines []string) []EventInfo {
	var events []EventInfo
	eventRegex := regexp.MustCompile(`event\s+(\w+)\s*\(([^)]*)\)\s*(anonymous)?\s*;`)

	for i, line := range lines {
		matches := eventRegex.FindStringSubmatch(line)
		if len(matches) >= 3 {
			params := p.parseParameters(matches[2])
			anonymous := matches[3] == "anonymous"

			events = append(events, EventInfo{
				Name:       matches[1],
				Parameters: params,
				Anonymous:  anonymous,
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, "event"),
				},
			})
		}
	}

	return events
}

// parseModifiers extracts modifier definitions
func (p *ASTParser) parseModifiers(source string, lines []string) []ModifierInfo {
	var modifiers []ModifierInfo
	modifierRegex := regexp.MustCompile(`modifier\s+(\w+)\s*\(([^)]*)\)\s*\{`)

	for i, line := range lines {
		matches := modifierRegex.FindStringSubmatch(line)
		if len(matches) >= 3 {
			params := p.parseParameters(matches[2])

			modifiers = append(modifiers, ModifierInfo{
				Name:       matches[1],
				Parameters: params,
				Location: SourceLocation{
					Line:   i + 1,
					Column: strings.Index(line, "modifier"),
				},
			})
		}
	}

	return modifiers
}

// Helper functions

func (p *ASTParser) parseParameters(paramStr string) []ParameterInfo {
	var params []ParameterInfo
	if strings.TrimSpace(paramStr) == "" {
		return params
	}

	paramList := strings.Split(paramStr, ",")
	for _, param := range paramList {
		param = strings.TrimSpace(param)
		parts := strings.Fields(param)
		if len(parts) >= 2 {
			params = append(params, ParameterInfo{
				Type: parts[0],
				Name: parts[1],
			})
		}
	}

	return params
}

func (p *ASTParser) parseModifiersAndVisibility(modifiersStr string) (string, string, []string) {
	visibility := "internal" // default
	stateMutability := ""
	var modifiers []string

	parts := strings.Fields(modifiersStr)
	for _, part := range parts {
		switch part {
		case "public", "private", "internal", "external":
			visibility = part
		case "pure", "view", "payable", "nonpayable":
			stateMutability = part
		case "virtual", "override":
			// Skip these for now
		default:
			if part != "" {
				modifiers = append(modifiers, part)
			}
		}
	}

	return visibility, stateMutability, modifiers
}

func (p *ASTParser) isSolcAvailable() bool {
	_, err := exec.LookPath("solc")
	return err == nil
}

func (p *ASTParser) convertSolcAST(astData map[string]interface{}) (*ContractAST, error) {
	// This would convert the solc AST format to our internal format
	// For now, return a basic structure
	return &ContractAST{}, nil
}
