package dependencies

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// VulnerabilityDatabase manages vulnerability data from multiple sources
type VulnerabilityDatabase struct {
	logger    *logrus.Logger
	sources   []VulnerabilitySource
	cache     map[string][]Vulnerability
	lastUpdate time.Time
}

// VulnerabilitySource interface for different vulnerability data sources
type VulnerabilitySource interface {
	GetVulnerabilities(packageName, version, ecosystem string) ([]Vulnerability, error)
	GetSourceName() string
	IsAvailable() bool
}

// OSVSource implements vulnerability data from OSV (Open Source Vulnerabilities)
type OSVSource struct {
	baseURL string
	client  *http.Client
}

// NPMAuditSource implements vulnerability data from NPM Audit API
type NPMAuditSource struct {
	baseURL string
	client  *http.Client
}

// GitHubAdvisorySource implements vulnerability data from GitHub Advisory Database
type GitHubAdvisorySource struct {
	baseURL string
	client  *http.Client
	token   string
}

// NewVulnerabilityDatabase creates a new vulnerability database
func NewVulnerabilityDatabase() *VulnerabilityDatabase {
	db := &VulnerabilityDatabase{
		logger: logrus.New(),
		cache:  make(map[string][]Vulnerability),
	}

	// Initialize vulnerability sources
	db.sources = []VulnerabilitySource{
		NewOSVSource(),
		NewNPMAuditSource(),
		NewGitHubAdvisorySource(),
	}

	return db
}

// GetVulnerabilities retrieves vulnerabilities for a specific package
func (vdb *VulnerabilityDatabase) GetVulnerabilities(packageName, version, ecosystem string) ([]Vulnerability, error) {
	// Input validation
	if packageName == "" {
		return nil, fmt.Errorf("package name cannot be empty")
	}
	if ecosystem == "" {
		return nil, fmt.Errorf("ecosystem cannot be empty")
	}

	cacheKey := fmt.Sprintf("%s:%s:%s", ecosystem, packageName, version)

	// Check cache first
	if vulns, exists := vdb.cache[cacheKey]; exists {
		vdb.logger.Debugf("Cache hit for %s", cacheKey)
		return vulns, nil
	}

	var allVulns []Vulnerability
	var errors []error

	// First check known vulnerabilities (fast local lookup)
	knownVulns := vdb.GetKnownVulnerabilities(packageName, ecosystem)
	if len(knownVulns) > 0 {
		allVulns = append(allVulns, knownVulns...)
		vdb.logger.Debugf("Found %d known vulnerabilities for %s", len(knownVulns), packageName)
	}

	// Query external sources
	for _, source := range vdb.sources {
		if !source.IsAvailable() {
			vdb.logger.Debugf("Source %s is not available", source.GetSourceName())
			continue
		}

		vulns, err := source.GetVulnerabilities(packageName, version, ecosystem)
		if err != nil {
			vdb.logger.Warnf("Failed to get vulnerabilities from %s: %v", source.GetSourceName(), err)
			errors = append(errors, fmt.Errorf("%s: %w", source.GetSourceName(), err))
			continue
		}

		allVulns = append(allVulns, vulns...)
		vdb.logger.Debugf("Found %d vulnerabilities from %s", len(vulns), source.GetSourceName())
	}

	// Remove duplicates
	allVulns = vdb.deduplicateVulnerabilities(allVulns)

	// Cache the results (even if empty)
	vdb.cache[cacheKey] = allVulns

	vdb.logger.Debugf("Total vulnerabilities found for %s: %d", packageName, len(allVulns))

	// Return error only if all sources failed and no known vulnerabilities found
	if len(allVulns) == 0 && len(errors) > 0 {
		return allVulns, fmt.Errorf("all vulnerability sources failed: %v", errors)
	}

	return allVulns, nil
}

// deduplicateVulnerabilities removes duplicate vulnerabilities
func (vdb *VulnerabilityDatabase) deduplicateVulnerabilities(vulns []Vulnerability) []Vulnerability {
	seen := make(map[string]bool)
	var unique []Vulnerability

	for _, vuln := range vulns {
		key := vuln.ID
		if vuln.CVE != "" {
			key = vuln.CVE
		}

		if !seen[key] {
			seen[key] = true
			unique = append(unique, vuln)
		}
	}

	return unique
}

// UpdateDatabase updates the vulnerability database from all sources
func (vdb *VulnerabilityDatabase) UpdateDatabase() error {
	vdb.logger.Info("Updating vulnerability database...")
	
	// Clear cache to force fresh data
	vdb.cache = make(map[string][]Vulnerability)
	vdb.lastUpdate = time.Now()
	
	vdb.logger.Info("Vulnerability database updated successfully")
	return nil
}

// NewOSVSource creates a new OSV vulnerability source
func NewOSVSource() *OSVSource {
	return &OSVSource{
		baseURL: "https://api.osv.dev",
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        10,
				IdleConnTimeout:     30 * time.Second,
				DisableCompression:  false,
			},
		},
	}
}

// GetVulnerabilities implements VulnerabilitySource for OSV
func (osv *OSVSource) GetVulnerabilities(packageName, version, ecosystem string) ([]Vulnerability, error) {
	// Convert ecosystem to OSV format
	osvEcosystem := osv.convertEcosystem(ecosystem)
	if osvEcosystem == "" {
		return []Vulnerability{}, nil
	}

	// Query OSV API
	queryURL := fmt.Sprintf("%s/v1/query", osv.baseURL)
	
	query := map[string]interface{}{
		"package": map[string]string{
			"name":      packageName,
			"ecosystem": osvEcosystem,
		},
		"version": version,
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, err
	}

	resp, err := osv.client.Post(queryURL, "application/json", strings.NewReader(string(queryJSON)))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return []Vulnerability{}, nil // No vulnerabilities found
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var osvResponse struct {
		Vulns []struct {
			ID      string `json:"id"`
			Summary string `json:"summary"`
			Details string `json:"details"`
			Aliases []string `json:"aliases"`
			Severity []struct {
				Type  string  `json:"type"`
				Score float64 `json:"score"`
			} `json:"severity"`
			References []struct {
				URL string `json:"url"`
			} `json:"references"`
			Published time.Time `json:"published"`
			Modified  time.Time `json:"modified"`
		} `json:"vulns"`
	}

	if err := json.Unmarshal(body, &osvResponse); err != nil {
		return nil, err
	}

	var vulnerabilities []Vulnerability
	for _, vuln := range osvResponse.Vulns {
		v := Vulnerability{
			ID:          vuln.ID,
			Title:       vuln.Summary,
			Description: vuln.Details,
			PublishedAt: vuln.Published,
		}

		// Extract CVE from aliases
		for _, alias := range vuln.Aliases {
			if strings.HasPrefix(alias, "CVE-") {
				v.CVE = alias
				break
			}
		}

		// Extract severity
		for _, sev := range vuln.Severity {
			if sev.Type == "CVSS_V3" {
				v.CVSS = sev.Score
				v.Severity = osv.cvssToSeverity(sev.Score)
				break
			}
		}

		// Extract references
		for _, ref := range vuln.References {
			v.References = append(v.References, ref.URL)
		}

		vulnerabilities = append(vulnerabilities, v)
	}

	return vulnerabilities, nil
}

// convertEcosystem converts package manager names to OSV ecosystem names
func (osv *OSVSource) convertEcosystem(ecosystem string) string {
	mapping := map[string]string{
		"npm":      "npm",
		"yarn":     "npm",
		"pip":      "PyPI",
		"poetry":   "PyPI",
		"cargo":    "crates.io",
		"go":       "Go",
		"composer": "Packagist",
		"maven":    "Maven",
		"gradle":   "Maven",
		"nuget":    "NuGet",
	}

	return mapping[ecosystem]
}

// cvssToSeverity converts CVSS score to severity level
func (osv *OSVSource) cvssToSeverity(score float64) string {
	if score >= 9.0 {
		return "critical"
	} else if score >= 7.0 {
		return "high"
	} else if score >= 4.0 {
		return "medium"
	} else {
		return "low"
	}
}

// GetSourceName returns the source name
func (osv *OSVSource) GetSourceName() string {
	return "OSV"
}

// IsAvailable checks if the OSV source is available
func (osv *OSVSource) IsAvailable() bool {
	resp, err := osv.client.Get(osv.baseURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

// NewNPMAuditSource creates a new NPM Audit vulnerability source
func NewNPMAuditSource() *NPMAuditSource {
	return &NPMAuditSource{
		baseURL: "https://registry.npmjs.org/-/npm/v1/security/audits",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetVulnerabilities implements VulnerabilitySource for NPM Audit
func (npm *NPMAuditSource) GetVulnerabilities(packageName, version, ecosystem string) ([]Vulnerability, error) {
	// Only handle npm packages
	if ecosystem != "npm" && ecosystem != "yarn" {
		return []Vulnerability{}, nil
	}

	// This would implement NPM audit API calls
	// For now, return empty slice as NPM audit requires package.json
	return []Vulnerability{}, nil
}

// GetSourceName returns the source name
func (npm *NPMAuditSource) GetSourceName() string {
	return "NPM Audit"
}

// IsAvailable checks if the NPM Audit source is available
func (npm *NPMAuditSource) IsAvailable() bool {
	return true // NPM audit is always available if npm is installed
}

// NewGitHubAdvisorySource creates a new GitHub Advisory vulnerability source
func NewGitHubAdvisorySource() *GitHubAdvisorySource {
	return &GitHubAdvisorySource{
		baseURL: "https://api.github.com/graphql",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetVulnerabilities implements VulnerabilitySource for GitHub Advisory
func (gh *GitHubAdvisorySource) GetVulnerabilities(packageName, version, ecosystem string) ([]Vulnerability, error) {
	// This would implement GitHub Advisory Database API calls
	// Requires GraphQL queries and authentication
	// For now, return empty slice
	return []Vulnerability{}, nil
}

// GetSourceName returns the source name
func (gh *GitHubAdvisorySource) GetSourceName() string {
	return "GitHub Advisory Database"
}

// IsAvailable checks if the GitHub Advisory source is available
func (gh *GitHubAdvisorySource) IsAvailable() bool {
	return gh.token != "" // Requires authentication token
}

// KnownVulnerabilities contains hardcoded known vulnerabilities for common packages
var KnownVulnerabilities = map[string][]Vulnerability{
	"npm:lodash": {
		{
			ID:          "GHSA-jf85-cpcp-j695",
			Title:       "Prototype Pollution in lodash",
			Description: "Versions of lodash before 4.17.12 are vulnerable to Prototype Pollution",
			Severity:    "high",
			CVSS:        7.5,
			CVE:         "CVE-2019-10744",
			CWE:         "CWE-1321",
			PatchedIn:   "4.17.12",
			References: []string{
				"https://github.com/advisories/GHSA-jf85-cpcp-j695",
				"https://nvd.nist.gov/vuln/detail/CVE-2019-10744",
			},
		},
	},
	"npm:axios": {
		{
			ID:          "GHSA-4w2v-q235-vp99",
			Title:       "Cross-Site Request Forgery in axios",
			Description: "Axios NPM package 0.21.0 contains a Server-Side Request Forgery (SSRF) vulnerability",
			Severity:    "medium",
			CVSS:        5.3,
			CVE:         "CVE-2020-28168",
			CWE:         "CWE-918",
			PatchedIn:   "0.21.1",
			References: []string{
				"https://github.com/advisories/GHSA-4w2v-q235-vp99",
			},
		},
	},
	"pip:django": {
		{
			ID:          "GHSA-2hrw-hx67-34x6",
			Title:       "SQL injection in Django",
			Description: "Django 2.2 before 2.2.25, 3.1 before 3.1.14, and 3.2 before 3.2.8 allows SQL injection",
			Severity:    "high",
			CVSS:        8.8,
			CVE:         "CVE-2021-35042",
			CWE:         "CWE-89",
			PatchedIn:   "2.2.25",
			References: []string{
				"https://github.com/advisories/GHSA-2hrw-hx67-34x6",
			},
		},
	},
	"pip:requests": {
		{
			ID:          "GHSA-j8r2-6x86-q33q",
			Title:       "Unintended leak of Proxy-Authorization header in requests",
			Description: "The Requests package through 2.25.1 for Python allows attackers to leak credentials",
			Severity:    "medium",
			CVSS:        6.1,
			CVE:         "CVE-2021-33503",
			CWE:         "CWE-200",
			PatchedIn:   "2.25.2",
			References: []string{
				"https://github.com/advisories/GHSA-j8r2-6x86-q33q",
			},
		},
	},
}

// GetKnownVulnerabilities returns known vulnerabilities for a package
func (vdb *VulnerabilityDatabase) GetKnownVulnerabilities(packageName, ecosystem string) []Vulnerability {
	key := fmt.Sprintf("%s:%s", ecosystem, packageName)
	if vulns, exists := KnownVulnerabilities[key]; exists {
		return vulns
	}
	return []Vulnerability{}
}
