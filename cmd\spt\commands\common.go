package commands

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"

	"github.com/olekukonko/tablewriter"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v2"
)

// loadConfig loads configuration from file or creates default
func loadConfig(cmd *cobra.Command) (*config.Config, error) {
	configPath, _ := cmd.Flags().GetString("config")

	var cfg *config.Config
	var err error

	if configPath != "" {
		cfg, err = config.LoadConfig(configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to load config from %s: %w", configPath, err)
		}
	} else {
		// Create default configuration
		cfg = &config.Config{
			Environment: "development",
			Security: config.SecurityConfig{
				Level: "medium",
				Rules: config.SecurityRulesConfig{
					PrivateKeyExposure: config.KeyExposureConfig{
						Enabled: true,
						Ethereum: config.EthereumConfig{
							PrivateKeys: true,
							Addresses:   true,
							Seeds:       true,
							RPC:         true,
						},
						Bitcoin: config.BitcoinConfig{
							PrivateKeys: true,
							Addresses:   true,
							Seeds:       true,
							RPC:         true,
						},
					},
				},
			},
			Scanning: config.ScanningConfig{
				Paths: config.PathsConfig{
					Include: []string{"*.sol", "*.js", "*.py", "*.go", "*.json", "*.yml", "*.yaml"},
					Exclude: []string{"node_modules", "vendor", ".git", "build", "dist"},
				},
				Timeout: 300, // 5 minutes
			},
			Logging: config.LoggingConfig{
				Level:  "info",
				Format: "text",
				Output: "stdout",
			},
		}
	}

	// Override with command line flags
	if logLevel, _ := cmd.Flags().GetString("log-level"); logLevel != "" {
		cfg.Logging.Level = logLevel
	}

	if verbose, _ := cmd.Flags().GetBool("verbose"); verbose {
		cfg.Logging.Level = "debug"
	}

	if quiet, _ := cmd.Flags().GetBool("quiet"); quiet {
		cfg.Logging.Level = "error"
	}

	return cfg, nil
}

// applyOptionsToConfig applies scan options to configuration
func applyOptionsToConfig(cfg *config.Config, opts *ScanOptions) {
	if len(opts.Include) > 0 {
		cfg.Scanning.Paths.Include = opts.Include
	}
	if len(opts.Exclude) > 0 {
		cfg.Scanning.Paths.Exclude = append(cfg.Scanning.Paths.Exclude, opts.Exclude...)
	}
	if opts.Timeout > 0 {
		cfg.Scanning.Timeout = int(opts.Timeout.Seconds())
	}
}

// displayResults displays scan results in the specified format
func displayResults(issues []models.SecurityIssue, opts *ScanOptions) error {
	if len(issues) == 0 {
		fmt.Printf("🎉 No security issues found!\n")
		return nil
	}

	// Sort issues by severity and file
	sort.Slice(issues, func(i, j int) bool {
		severityOrder := map[string]int{"critical": 4, "high": 3, "medium": 2, "low": 1}

		severityI := severityOrder[strings.ToLower(issues[i].Severity)]
		severityJ := severityOrder[strings.ToLower(issues[j].Severity)]

		if severityI != severityJ {
			return severityI > severityJ
		}

		if issues[i].File != issues[j].File {
			return issues[i].File < issues[j].File
		}

		return issues[i].Line < issues[j].Line
	})

	// Display summary
	displaySummary(issues)

	// Display detailed results based on format
	switch strings.ToLower(opts.Format) {
	case "table":
		return displayTableResults(issues)
	case "json":
		return displayJSONResults(issues)
	case "yaml":
		return displayYAMLResults(issues)
	case "csv":
		return displayCSVResults(issues)
	default:
		return fmt.Errorf("unsupported output format: %s", opts.Format)
	}
}

// displaySummary shows a summary of found issues
func displaySummary(issues []models.SecurityIssue) {
	fmt.Printf("\n📋 Security Issues Summary:\n")
	fmt.Printf("═══════════════════════════\n")

	// Count by severity
	severityCounts := make(map[string]int)
	chainCounts := make(map[string]int)
	categoryCounts := make(map[string]int)

	for _, issue := range issues {
		severityCounts[strings.ToLower(issue.Severity)]++
		chainCounts[issue.Chain]++
		categoryCounts[issue.Category]++
	}

	// Display severity breakdown
	fmt.Printf("By Severity:\n")
	for _, severity := range []string{"critical", "high", "medium", "low"} {
		count := severityCounts[severity]
		if count > 0 {
			emoji := getSeverityEmoji(severity)
			fmt.Printf("  %s %s: %d\n", emoji, strings.Title(severity), count)
		}
	}

	// Display chain breakdown
	if len(chainCounts) > 1 {
		fmt.Printf("\nBy Blockchain:\n")
		for chain, count := range chainCounts {
			if count > 0 {
				emoji := getChainEmoji(chain)
				fmt.Printf("  %s %s: %d\n", emoji, strings.Title(chain), count)
			}
		}
	}

	// Display category breakdown
	fmt.Printf("\nBy Category:\n")
	for category, count := range categoryCounts {
		if count > 0 {
			emoji := getCategoryEmoji(category)
			fmt.Printf("  %s %s: %d\n", emoji, strings.Title(category), count)
		}
	}

	fmt.Printf("\nTotal Issues: %d\n\n", len(issues))
}

// displayTableResults displays results in table format
func displayTableResults(issues []models.SecurityIssue) error {
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Severity", "Type", "File", "Line", "Title", "Chain"})
	table.SetBorder(true)
	table.SetRowSeparator("-")
	table.SetCenterSeparator("|")
	table.SetColumnSeparator("|")
	table.SetHeaderAlignment(tablewriter.ALIGN_CENTER)
	table.SetAlignment(tablewriter.ALIGN_LEFT)

	// Set colors for severity levels
	table.SetHeaderColor(
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
		tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiWhiteColor},
	)

	for _, issue := range issues {
		severity := strings.ToUpper(issue.Severity)
		severityColor := getSeverityColor(issue.Severity)

		row := []string{
			severity,
			issue.Type,
			filepath.Base(issue.File),
			strconv.Itoa(issue.Line),
			truncateString(issue.Title, 50),
			issue.Chain,
		}

		table.Rich(row, []tablewriter.Colors{
			severityColor,
			{},
			{},
			{},
			{},
			{},
		})
	}

	fmt.Printf("🔍 Detailed Results:\n")
	fmt.Printf("═══════════════════\n")
	table.Render()

	// Display detailed information for critical and high severity issues
	criticalAndHigh := filterIssuesBySeverity(issues, "high")
	if len(criticalAndHigh) > 0 {
		fmt.Printf("\n🚨 Critical & High Severity Details:\n")
		fmt.Printf("═══════════════════════════════════\n")

		for i, issue := range criticalAndHigh {
			if i > 0 {
				fmt.Printf("\n" + strings.Repeat("-", 80) + "\n")
			}

			fmt.Printf("🔴 %s [%s]\n", issue.Title, strings.ToUpper(issue.Severity))
			fmt.Printf("📁 File: %s:%d\n", issue.File, issue.Line)
			fmt.Printf("🏷️  Type: %s\n", issue.Type)
			fmt.Printf("⛓️  Chain: %s\n", issue.Chain)
			fmt.Printf("📝 Description: %s\n", issue.Description)

			if issue.Code != "" {
				fmt.Printf("💻 Code: %s\n", issue.Code)
			}

			if issue.Suggestion != "" {
				fmt.Printf("💡 Suggestion: %s\n", issue.Suggestion)
			}

			if len(issue.References) > 0 {
				fmt.Printf("🔗 References:\n")
				for _, ref := range issue.References {
					fmt.Printf("   - %s\n", ref)
				}
			}
		}
	}

	return nil
}

// displayJSONResults displays results in JSON format
func displayJSONResults(issues []models.SecurityIssue) error {
	output := map[string]interface{}{
		"scan_time":    time.Now().Format(time.RFC3339),
		"total_issues": len(issues),
		"issues":       issues,
	}

	encoder := json.NewEncoder(os.Stdout)
	encoder.SetIndent("", "  ")
	return encoder.Encode(output)
}

// displayYAMLResults displays results in YAML format
func displayYAMLResults(issues []models.SecurityIssue) error {
	output := map[string]interface{}{
		"scan_time":    time.Now().Format(time.RFC3339),
		"total_issues": len(issues),
		"issues":       issues,
	}

	encoder := yaml.NewEncoder(os.Stdout)
	return encoder.Encode(output)
}

// displayCSVResults displays results in CSV format
func displayCSVResults(issues []models.SecurityIssue) error {
	writer := csv.NewWriter(os.Stdout)
	defer writer.Flush()

	// Write header
	header := []string{"Severity", "Type", "File", "Line", "Title", "Description", "Chain", "Category", "CWE", "OWASP"}
	if err := writer.Write(header); err != nil {
		return err
	}

	// Write data
	for _, issue := range issues {
		record := []string{
			issue.Severity,
			issue.Type,
			issue.File,
			strconv.Itoa(issue.Line),
			issue.Title,
			issue.Description,
			issue.Chain,
			issue.Category,
			issue.CWE,
			issue.OWASP,
		}
		if err := writer.Write(record); err != nil {
			return err
		}
	}

	return nil
}

// saveResults saves results to a file
func saveResults(issues []models.SecurityIssue, opts *ScanOptions) error {
	file, err := os.Create(opts.Output)
	if err != nil {
		return err
	}
	defer file.Close()

	output := map[string]interface{}{
		"scan_time":    time.Now().Format(time.RFC3339),
		"total_issues": len(issues),
		"scan_config": map[string]interface{}{
			"path":      opts.Path,
			"chain":     opts.Chain,
			"type":      opts.Type,
			"severity":  opts.Severity,
			"recursive": opts.Recursive,
		},
		"issues": issues,
	}

	switch strings.ToLower(opts.Format) {
	case "json":
		encoder := json.NewEncoder(file)
		encoder.SetIndent("", "  ")
		return encoder.Encode(output)
	case "yaml":
		encoder := yaml.NewEncoder(file)
		return encoder.Encode(output)
	default:
		return fmt.Errorf("unsupported output format for file: %s", opts.Format)
	}
}

// Helper functions for display formatting

func getSeverityEmoji(severity string) string {
	switch strings.ToLower(severity) {
	case "critical":
		return "🔴"
	case "high":
		return "🟠"
	case "medium":
		return "🟡"
	case "low":
		return "🟢"
	default:
		return "⚪"
	}
}

func getChainEmoji(chain string) string {
	switch strings.ToLower(chain) {
	case "ethereum":
		return "⟠"
	case "bitcoin":
		return "₿"
	case "general":
		return "🔧"
	default:
		return "⛓️"
	}
}

func getCategoryEmoji(category string) string {
	switch strings.ToLower(category) {
	case "smart_contract":
		return "📜"
	case "wallet":
		return "💰"
	case "dependency":
		return "📦"
	case "environment":
		return "🌍"
	case "cicd":
		return "🔄"
	default:
		return "🔍"
	}
}

func getSeverityColor(severity string) tablewriter.Colors {
	switch strings.ToLower(severity) {
	case "critical":
		return tablewriter.Colors{tablewriter.Bold, tablewriter.FgHiRedColor}
	case "high":
		return tablewriter.Colors{tablewriter.Bold, tablewriter.FgRedColor}
	case "medium":
		return tablewriter.Colors{tablewriter.Bold, tablewriter.FgYellowColor}
	case "low":
		return tablewriter.Colors{tablewriter.Bold, tablewriter.FgGreenColor}
	default:
		return tablewriter.Colors{}
	}
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}
