import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { AuthService } from './auth.service';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface ScanProgress {
  scanId: string;
  status: 'starting' | 'scanning' | 'analyzing' | 'completed' | 'failed';
  progress: number; // 0-100
  currentFile?: string;
  filesScanned: number;
  totalFiles: number;
  issuesFound: number;
  message?: string;
}

export interface ScanNotification {
  id: string;
  type: 'scan_started' | 'scan_completed' | 'scan_failed' | 'issue_found' | 'system_alert';
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'success';
  timestamp: string;
  read: boolean;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private reconnectTimer: any;

  // Connection state
  private connectionStateSubject = new BehaviorSubject<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  public connectionState$ = this.connectionStateSubject.asObservable();

  // Scan progress tracking
  private scanProgressSubject = new BehaviorSubject<ScanProgress | null>(null);
  public scanProgress$ = this.scanProgressSubject.asObservable();

  // Notifications
  private notificationsSubject = new BehaviorSubject<ScanNotification[]>([]);
  public notifications$ = this.notificationsSubject.asObservable();

  // Real-time messages
  private messagesSubject = new Subject<WebSocketMessage>();
  public messages$ = this.messagesSubject.asObservable();

  constructor(private authService: AuthService) {
    // Auto-connect when user is authenticated
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.connect();
      } else {
        this.disconnect();
      }
    });
  }

  connect(): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    this.connectionStateSubject.next('connecting');
    
    const token = this.authService.getToken();
    const wsUrl = `ws://localhost:8080/ws${token ? '?token=' + token : ''}`;
    
    try {
      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.connectionStateSubject.next('error');
      this.scheduleReconnect();
    }
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.connectionStateSubject.next('disconnected');
    this.reconnectAttempts = 0;
  }

  sendMessage(message: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }

  getCurrentConnectionState(): 'connecting' | 'connected' | 'disconnected' | 'error' {
    return this.connectionStateSubject.value;
  }

  // Scan-specific methods
  subscribeScanProgress(scanId: string): void {
    this.sendMessage({
      type: 'subscribe_scan',
      scanId: scanId
    });
  }

  unsubscribeScanProgress(scanId: string): void {
    this.sendMessage({
      type: 'unsubscribe_scan',
      scanId: scanId
    });
  }

  // Notification methods
  markNotificationAsRead(notificationId: string): void {
    const notifications = this.notificationsSubject.value;
    const updatedNotifications = notifications.map(n => 
      n.id === notificationId ? { ...n, read: true } : n
    );
    this.notificationsSubject.next(updatedNotifications);
  }

  clearAllNotifications(): void {
    this.notificationsSubject.next([]);
  }

  getUnreadNotificationCount(): number {
    return this.notificationsSubject.value.filter(n => !n.read).length;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      console.log('WebSocket connected');
      this.connectionStateSubject.next('connected');
      this.reconnectAttempts = 0;
      
      // Send authentication if needed
      const token = this.authService.getToken();
      if (token) {
        this.sendMessage({
          type: 'authenticate',
          token: token
        });
      }
    };

    this.socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.connectionStateSubject.next('disconnected');
      
      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.connectionStateSubject.next('error');
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    this.messagesSubject.next(message);

    switch (message.type) {
      case 'scan_progress':
        this.handleScanProgress(message.data);
        break;
      case 'scan_notification':
        this.handleScanNotification(message.data);
        break;
      case 'system_notification':
        this.handleSystemNotification(message.data);
        break;
      case 'authentication_success':
        console.log('WebSocket authentication successful');
        break;
      case 'authentication_failed':
        console.error('WebSocket authentication failed');
        this.authService.logout();
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  private handleScanProgress(data: ScanProgress): void {
    this.scanProgressSubject.next(data);
    
    // Create notification for scan completion
    if (data.status === 'completed' || data.status === 'failed') {
      const notification: ScanNotification = {
        id: `scan_${data.scanId}_${Date.now()}`,
        type: data.status === 'completed' ? 'scan_completed' : 'scan_failed',
        title: data.status === 'completed' ? 'Scan Completed' : 'Scan Failed',
        message: data.status === 'completed' 
          ? `Scan completed with ${data.issuesFound} issues found`
          : data.message || 'Scan failed due to an error',
        severity: data.status === 'completed' ? 'success' : 'error',
        timestamp: new Date().toISOString(),
        read: false,
        data: data
      };
      this.addNotification(notification);
    }
  }

  private handleScanNotification(data: any): void {
    const notification: ScanNotification = {
      id: data.id || `notification_${Date.now()}`,
      type: data.type || 'system_alert',
      title: data.title,
      message: data.message,
      severity: data.severity || 'info',
      timestamp: data.timestamp || new Date().toISOString(),
      read: false,
      data: data.data
    };
    this.addNotification(notification);
  }

  private handleSystemNotification(data: any): void {
    const notification: ScanNotification = {
      id: data.id || `system_${Date.now()}`,
      type: 'system_alert',
      title: data.title || 'System Notification',
      message: data.message,
      severity: data.severity || 'info',
      timestamp: data.timestamp || new Date().toISOString(),
      read: false,
      data: data.data
    };
    this.addNotification(notification);
  }

  private addNotification(notification: ScanNotification): void {
    const notifications = this.notificationsSubject.value;
    const updatedNotifications = [notification, ...notifications].slice(0, 50); // Keep only last 50
    this.notificationsSubject.next(updatedNotifications);
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      if (this.authService.isAuthenticated()) {
        this.connect();
      }
    }, delay);
  }

  // Mock methods for development
  mockScanProgress(scanId: string): void {
    const steps = [
      { status: 'starting', progress: 0, message: 'Initializing scan...' },
      { status: 'scanning', progress: 25, message: 'Scanning smart contracts...' },
      { status: 'scanning', progress: 50, message: 'Analyzing dependencies...' },
      { status: 'analyzing', progress: 75, message: 'Generating report...' },
      { status: 'completed', progress: 100, message: 'Scan completed successfully' }
    ];

    let stepIndex = 0;
    const interval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex];
        const progress: ScanProgress = {
          scanId: scanId,
          status: step.status as any,
          progress: step.progress,
          currentFile: stepIndex > 0 ? `contract_${stepIndex}.sol` : undefined,
          filesScanned: stepIndex * 5,
          totalFiles: 20,
          issuesFound: stepIndex * 2,
          message: step.message
        };
        this.scanProgressSubject.next(progress);
        stepIndex++;
      } else {
        clearInterval(interval);
      }
    }, 2000);
  }
}
