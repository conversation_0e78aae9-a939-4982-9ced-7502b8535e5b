package bitcoin

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"

	"github.com/sirupsen/logrus"
)

// WalletAnalyzer analyzes Bitcoin wallet security patterns
type WalletAnalyzer struct {
	logger *logrus.Logger
}

// WalletType represents different types of Bitcoin wallets
type WalletType int

const (
	WalletTypeUnknown  WalletType = iota
	WalletTypeLegacy              // P2PKH (1...)
	WalletTypeScript              // P2SH (3...)
	WalletTypeSegWit              // Bech32 (bc1...)
	WalletTypeMultisig            // Multisig wallet
	WalletTypeHD                  // Hierarchical Deterministic
	WalletTypePaper               // Paper wallet
	WalletTypeHardware            // Hardware wallet
)

// WalletInfo represents wallet information
type WalletInfo struct {
	Type        WalletType
	Addresses   []string
	PrivateKeys []string
	PublicKeys  []string
	Seeds       []string
	Mnemonics   []string
	IsHD        bool
	IsMultisig  bool
	IsEncrypted bool
	HasBackup   bool
}

// PrivateKeyInfo represents private key information
type PrivateKeyInfo struct {
	Key        string
	Format     string // WIF, hex, etc.
	Network    string // mainnet, testnet
	Compressed bool
	Valid      bool
}

// AddressInfo represents address information
type AddressInfo struct {
	Address string
	Type    string
	Network string
	Valid   bool
}

// NewWalletAnalyzer creates a new wallet analyzer
func NewWalletAnalyzer() *WalletAnalyzer {
	return &WalletAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzeWallet analyzes wallet security patterns
func (wa *WalletAnalyzer) AnalyzeWallet(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Extract wallet information
	walletInfo := wa.extractWalletInfo(content)

	// Analyze private key security
	issues = append(issues, wa.analyzePrivateKeys(walletInfo, filePath)...)

	// Analyze address patterns
	issues = append(issues, wa.analyzeAddresses(walletInfo, filePath)...)

	// Analyze seed/mnemonic security
	issues = append(issues, wa.analyzeSeedSecurity(walletInfo, filePath)...)

	// Check for wallet-related security patterns
	issues = append(issues, wa.checkWalletPatterns(filePath, content)...)

	return issues, nil
}

// extractWalletInfo extracts wallet information from content
func (wa *WalletAnalyzer) extractWalletInfo(content string) WalletInfo {
	info := WalletInfo{
		Type:        WalletTypeUnknown,
		Addresses:   []string{},
		PrivateKeys: []string{},
		PublicKeys:  []string{},
		Seeds:       []string{},
		Mnemonics:   []string{},
	}

	// Extract Bitcoin addresses
	info.Addresses = wa.extractAddresses(content)

	// Extract private keys
	info.PrivateKeys = wa.extractPrivateKeys(content)

	// Extract public keys
	info.PublicKeys = wa.extractPublicKeys(content)

	// Extract seeds and mnemonics
	info.Seeds = wa.extractSeeds(content)
	info.Mnemonics = wa.extractMnemonics(content)

	// Determine wallet characteristics
	info.IsHD = wa.detectHDWallet(content)
	info.IsMultisig = wa.detectMultisig(content)
	info.IsEncrypted = wa.detectEncryption(content)
	info.HasBackup = wa.detectBackup(content)

	// Determine wallet type
	info.Type = wa.determineWalletType(info)

	return info
}

// extractAddresses extracts Bitcoin addresses from content
func (wa *WalletAnalyzer) extractAddresses(content string) []string {
	var addresses []string

	// Pattern for Bitcoin addresses
	patterns := []string{
		`\b1[1-9A-HJ-NP-Za-km-z]{25,34}\b`, // Legacy P2PKH
		`\b3[1-9A-HJ-NP-Za-km-z]{25,34}\b`, // P2SH
		`\bbc1[a-z0-9]{39,59}\b`,           // Bech32 SegWit
		`\btb1[a-z0-9]{39,59}\b`,           // Testnet Bech32
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllString(content, -1)
		addresses = append(addresses, matches...)
	}

	return wa.removeDuplicates(addresses)
}

// extractPrivateKeys extracts private keys from content
func (wa *WalletAnalyzer) extractPrivateKeys(content string) []string {
	var keys []string

	// Pattern for WIF private keys
	wifPattern := regexp.MustCompile(`\b[5KL][1-9A-HJ-NP-Za-km-z]{50,51}\b`)
	wifMatches := wifPattern.FindAllString(content, -1)
	keys = append(keys, wifMatches...)

	// Pattern for compressed WIF private keys
	compressedWifPattern := regexp.MustCompile(`\b[KL][1-9A-HJ-NP-Za-km-z]{51}\b`)
	compressedMatches := compressedWifPattern.FindAllString(content, -1)
	keys = append(keys, compressedMatches...)

	// Pattern for hex private keys (64 hex characters)
	hexPattern := regexp.MustCompile(`\b[0-9a-fA-F]{64}\b`)
	hexMatches := hexPattern.FindAllString(content, -1)

	// Validate hex keys (basic check)
	for _, hexKey := range hexMatches {
		if wa.isValidPrivateKeyHex(hexKey) {
			keys = append(keys, hexKey)
		}
	}

	return wa.removeDuplicates(keys)
}

// extractPublicKeys extracts public keys from content
func (wa *WalletAnalyzer) extractPublicKeys(content string) []string {
	var keys []string

	// Pattern for compressed public keys (66 hex characters starting with 02 or 03)
	compressedPattern := regexp.MustCompile(`\b0[23][0-9a-fA-F]{64}\b`)
	compressedMatches := compressedPattern.FindAllString(content, -1)
	keys = append(keys, compressedMatches...)

	// Pattern for uncompressed public keys (130 hex characters starting with 04)
	uncompressedPattern := regexp.MustCompile(`\b04[0-9a-fA-F]{128}\b`)
	uncompressedMatches := uncompressedPattern.FindAllString(content, -1)
	keys = append(keys, uncompressedMatches...)

	return wa.removeDuplicates(keys)
}

// extractSeeds extracts seed phrases from content
func (wa *WalletAnalyzer) extractSeeds(content string) []string {
	var seeds []string

	// Pattern for hex seeds (typically 32-64 bytes in hex)
	hexSeedPattern := regexp.MustCompile(`(?i)(seed|entropy)\s*[:=]\s*["']?([0-9a-fA-F]{64,128})["']?`)
	matches := hexSeedPattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 2 {
			seeds = append(seeds, match[2])
		}
	}

	return wa.removeDuplicates(seeds)
}

// extractMnemonics extracts mnemonic phrases from content
func (wa *WalletAnalyzer) extractMnemonics(content string) []string {
	var mnemonics []string

	// Pattern for mnemonic phrases (12, 15, 18, 21, or 24 words)
	mnemonicPattern := regexp.MustCompile(`(?i)(mnemonic|phrase|words)\s*[:=]\s*["']([a-z\s]+)["']`)
	matches := mnemonicPattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 2 {
			words := strings.Fields(match[2])
			if len(words) >= 12 && len(words) <= 24 && len(words)%3 == 0 {
				mnemonics = append(mnemonics, match[2])
			}
		}
	}

	return wa.removeDuplicates(mnemonics)
}

// detectHDWallet detects if content relates to HD wallets
func (wa *WalletAnalyzer) detectHDWallet(content string) bool {
	hdPatterns := []string{
		`(?i)hd.*wallet`,
		`(?i)hierarchical.*deterministic`,
		`(?i)bip32|bip44|bip49|bip84`,
		`(?i)xpub|xprv|ypub|yprv|zpub|zprv`,
		`(?i)derivation.*path`,
		`(?i)master.*key`,
	}

	for _, pattern := range hdPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return true
		}
	}

	return false
}

// detectMultisig detects if content relates to multisig wallets
func (wa *WalletAnalyzer) detectMultisig(content string) bool {
	multisigPatterns := []string{
		`(?i)multisig|multi.*sig`,
		`(?i)[0-9]+.*of.*[0-9]+`,
		`(?i)m.*of.*n`,
		`(?i)threshold.*signature`,
		`(?i)OP_CHECKMULTISIG`,
	}

	for _, pattern := range multisigPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return true
		}
	}

	return false
}

// detectEncryption detects if wallet is encrypted
func (wa *WalletAnalyzer) detectEncryption(content string) bool {
	encryptionPatterns := []string{
		`(?i)encrypt|password|passphrase`,
		`(?i)aes|cipher`,
		`(?i)wallet.*lock`,
		`(?i)decrypt`,
	}

	for _, pattern := range encryptionPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return true
		}
	}

	return false
}

// detectBackup detects if backup mechanisms are present
func (wa *WalletAnalyzer) detectBackup(content string) bool {
	backupPatterns := []string{
		`(?i)backup|restore`,
		`(?i)recovery.*phrase`,
		`(?i)seed.*backup`,
		`(?i)wallet.*backup`,
	}

	for _, pattern := range backupPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return true
		}
	}

	return false
}

// determineWalletType determines the wallet type based on extracted information
func (wa *WalletAnalyzer) determineWalletType(info WalletInfo) WalletType {
	if info.IsMultisig {
		return WalletTypeMultisig
	}

	if info.IsHD {
		return WalletTypeHD
	}

	// Check address types
	for _, addr := range info.Addresses {
		if strings.HasPrefix(addr, "1") {
			return WalletTypeLegacy
		} else if strings.HasPrefix(addr, "3") {
			return WalletTypeScript
		} else if strings.HasPrefix(addr, "bc1") {
			return WalletTypeSegWit
		}
	}

	// Check for paper wallet indicators
	if len(info.PrivateKeys) > 0 && !info.IsEncrypted {
		return WalletTypePaper
	}

	return WalletTypeUnknown
}

// analyzePrivateKeys analyzes private key security
func (wa *WalletAnalyzer) analyzePrivateKeys(info WalletInfo, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for exposed private keys
	for i, key := range info.PrivateKeys {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_exposed_private_key_%d", i),
			Type:        "private_key_exposure",
			Severity:    "critical",
			Title:       "Bitcoin Private Key Exposure",
			Description: "Bitcoin private key found in source code",
			File:        filePath,
			Line:        1,
			Code:        wa.maskPrivateKey(key),
			Chain:       "bitcoin",
			Category:    "wallet",
			CWE:         "CWE-798",
			OWASP:       "A02:2021",
			Suggestion:  "Remove private keys from code and use secure key management",
			References:  []string{"https://bitcoin.org/en/secure-your-wallet"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for weak private key patterns
	for _, key := range info.PrivateKeys {
		if wa.isWeakPrivateKey(key) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_weak_private_key_%s", key[:8]),
				Type:        "weak_private_key",
				Severity:    "high",
				Title:       "Weak Bitcoin Private Key",
				Description: "Private key appears to follow a predictable pattern",
				File:        filePath,
				Line:        1,
				Code:        wa.maskPrivateKey(key),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Use cryptographically secure random number generation for private keys",
				References:  []string{"https://bitcoin.org/en/developer-guide#private-key-formats"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// analyzeAddresses analyzes address security patterns
func (wa *WalletAnalyzer) analyzeAddresses(info WalletInfo, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for address reuse patterns
	if len(info.Addresses) > 1 {
		// This is a simplified check - in practice, you'd need more context
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_potential_address_reuse_%d", time.Now().UnixNano()),
			Type:        "address_reuse",
			Severity:    "medium",
			Title:       "Potential Address Reuse",
			Description: "Multiple addresses found - ensure addresses are not being reused",
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("Found %d addresses", len(info.Addresses)),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Use a new address for each transaction to maintain privacy",
			References:  []string{"https://en.bitcoin.it/wiki/Address_reuse"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for testnet addresses in production code
	for _, addr := range info.Addresses {
		if wa.isTestnetAddress(addr) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_testnet_address_%s", addr[:8]),
				Type:        "testnet_address",
				Severity:    "medium",
				Title:       "Testnet Address in Code",
				Description: "Testnet Bitcoin address found - ensure this is intentional",
				File:        filePath,
				Line:        1,
				Code:        addr,
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Use mainnet addresses for production or clearly mark test code",
				References:  []string{"https://bitcoin.org/en/developer-guide#testnet"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// analyzeSeedSecurity analyzes seed and mnemonic security
func (wa *WalletAnalyzer) analyzeSeedSecurity(info WalletInfo, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for exposed seeds
	for i, seed := range info.Seeds {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_exposed_seed_%d", i),
			Type:        "seed_exposure",
			Severity:    "critical",
			Title:       "Bitcoin Seed Exposure",
			Description: "Bitcoin wallet seed found in source code",
			File:        filePath,
			Line:        1,
			Code:        wa.maskSeed(seed),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Remove seeds from code and use secure storage",
			References:  []string{"https://bitcoin.org/en/secure-your-wallet"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for exposed mnemonics
	for i, mnemonic := range info.Mnemonics {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_exposed_mnemonic_%d", i),
			Type:        "mnemonic_exposure",
			Severity:    "critical",
			Title:       "Bitcoin Mnemonic Exposure",
			Description: "Bitcoin mnemonic phrase found in source code",
			File:        filePath,
			Line:        1,
			Code:        wa.maskMnemonic(mnemonic),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Remove mnemonic phrases from code and use secure storage",
			References:  []string{"https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// checkWalletPatterns checks for wallet-related security patterns
func (wa *WalletAnalyzer) checkWalletPatterns(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	// Check for hardcoded wallet passwords
	passwordPattern := regexp.MustCompile(`(?i)(wallet.*password|password.*wallet)\s*[:=]\s*["']([^"']+)["']`)

	for i, line := range lines {
		if matches := passwordPattern.FindStringSubmatch(line); len(matches) > 2 {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_hardcoded_password_%d", i+1),
				Type:        "hardcoded_password",
				Severity:    "high",
				Title:       "Hardcoded Wallet Password",
				Description: "Wallet password is hardcoded in source code",
				File:        filePath,
				Line:        i + 1,
				Code:        wa.maskPassword(line),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Use environment variables or secure configuration for passwords",
				References:  []string{"https://bitcoin.org/en/secure-your-wallet"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	// Check for insecure RPC configurations
	rpcPattern := regexp.MustCompile(`(?i)rpc(user|password|auth)\s*[:=]\s*["']?([^"'\s]+)["']?`)

	for i, line := range lines {
		if rpcPattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_insecure_rpc_%d", i+1),
				Type:        "insecure_rpc",
				Severity:    "medium",
				Title:       "Insecure RPC Configuration",
				Description: "Bitcoin RPC credentials found in source code",
				File:        filePath,
				Line:        i + 1,
				Code:        wa.maskPassword(line),
				Chain:       "bitcoin",
				Category:    "environment",
				Suggestion:  "Use secure configuration files with proper permissions",
				References:  []string{"https://bitcoin.org/en/developer-reference#remote-procedure-calls-rpcs"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// Helper functions

func (wa *WalletAnalyzer) removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

func (wa *WalletAnalyzer) isValidPrivateKeyHex(hexKey string) bool {
	// Basic validation - check if it's a valid hex string of correct length
	if len(hexKey) != 64 {
		return false
	}

	// Check if all characters are valid hex
	for _, char := range hexKey {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return false
		}
	}

	return true
}

func (wa *WalletAnalyzer) isWeakPrivateKey(key string) bool {
	// Check for obviously weak patterns
	weakPatterns := []string{
		"000000000000000000000000000000000000000000000000000000000000000",
		"***************************************************************",
		"123456789abcdef123456789abcdef123456789abcdef123456789abcdef1234",
		"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
	}

	keyLower := strings.ToLower(key)
	for _, pattern := range weakPatterns {
		if strings.Contains(keyLower, pattern) {
			return true
		}
	}

	return false
}

func (wa *WalletAnalyzer) isTestnetAddress(address string) bool {
	// Testnet addresses start with 'm', 'n', '2', or 'tb1'
	return strings.HasPrefix(address, "m") ||
		strings.HasPrefix(address, "n") ||
		strings.HasPrefix(address, "2") ||
		strings.HasPrefix(address, "tb1")
}

func (wa *WalletAnalyzer) maskPrivateKey(key string) string {
	if len(key) <= 8 {
		return "****"
	}
	return key[:4] + "****" + key[len(key)-4:]
}

func (wa *WalletAnalyzer) maskSeed(seed string) string {
	if len(seed) <= 16 {
		return "****"
	}
	return seed[:8] + "****" + seed[len(seed)-8:]
}

func (wa *WalletAnalyzer) maskMnemonic(mnemonic string) string {
	words := strings.Fields(mnemonic)
	if len(words) <= 4 {
		return "*** *** ***"
	}
	return words[0] + " *** *** " + words[len(words)-1]
}

func (wa *WalletAnalyzer) maskPassword(line string) string {
	// Replace password values with asterisks
	passwordPattern := regexp.MustCompile(`([:=]\s*["']?)([^"'\s]+)(["']?)`)
	return passwordPattern.ReplaceAllString(line, "${1}****${3}")
}
