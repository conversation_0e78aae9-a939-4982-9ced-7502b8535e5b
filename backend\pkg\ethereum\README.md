# Ethereum Security Module

## Overview

The Ethereum Security Module provides comprehensive security analysis for Ethereum smart contracts written in Solidity. It includes advanced AST parsing, vulnerability detection, gas optimization analysis, and contract quality assessment.

## Features

### 🔍 Advanced Vulnerability Detection
- **Reentrancy Attacks**: Detects external calls followed by state changes
- **Integer Overflow/Underflow**: Checks for arithmetic operations without protection
- **Access Control Issues**: Identifies missing or inadequate access controls
- **Unchecked External Calls**: Finds external calls without return value validation
- **Timestamp Dependence**: Detects dangerous use of block.timestamp
- **Denial of Service**: Identifies potential DoS through gas limits or dependencies
- **Front-Running**: Detects transaction ordering dependencies
- **Uninitialized Storage**: Finds uninitialized storage pointers
- **Delegatecall Injection**: Identifies dangerous delegatecall usage
- **Signature Malleability**: Detects ECDSA signature vulnerabilities

### ⚡ Gas Optimization Analysis
- **Storage Optimization**: Identifies redundant storage operations
- **Loop Optimization**: Detects inefficient loop patterns
- **Function Visibility**: Suggests optimal visibility modifiers
- **Constant Variables**: Identifies variables that could be constant
- **Struct Packing**: Analyzes struct field ordering for gas efficiency
- **Array Length Caching**: Detects repeated array length access
- **Event Optimization**: Suggests indexed parameters for events

### 📊 Contract Analysis
- **Complexity Metrics**: Calculates cyclomatic complexity
- **Security Scoring**: Provides security risk assessment (0-100)
- **Gas Efficiency Scoring**: Evaluates gas optimization level
- **Code Quality Scoring**: Assesses overall code quality
- **Dependency Analysis**: Maps contract dependencies and imports

### 🌳 AST Parsing
- **Solidity Compiler Integration**: Uses `solc` when available
- **Regex Fallback**: Provides parsing when compiler unavailable
- **Comprehensive Extraction**: Parses contracts, functions, events, modifiers
- **Inheritance Analysis**: Tracks contract inheritance relationships

## Architecture

```
ethereum/
├── scanner.go              # Main scanner interface
├── ast_parser.go           # Solidity AST parsing
├── vulnerability_detector.go # Advanced vulnerability detection
├── gas_analyzer.go         # Gas optimization analysis
├── contract_analyzer.go    # Contract quality and metrics
└── scanner_test.go         # Comprehensive test suite
```

## Usage

### Basic Scanning

```go
import "blockchain-spt/spt/backend/pkg/ethereum"

// Create scanner
scanner, err := ethereum.NewScanner(config)
if err != nil {
    log.Fatal(err)
}

// Scan a single file
issues, err := scanner.ScanFile(ctx, "contracts/MyContract.sol")
if err != nil {
    log.Fatal(err)
}

// Scan entire project
issues, err := scanner.ScanProject(ctx, "./contracts")
if err != nil {
    log.Fatal(err)
}
```

### Advanced Analysis

```go
// Create specialized analyzers
vulnDetector := ethereum.NewVulnerabilityDetector()
gasAnalyzer := ethereum.NewGasAnalyzer()
contractAnalyzer := ethereum.NewContractAnalyzer()

// Detect vulnerabilities
issues, err := vulnDetector.DetectVulnerabilities(filePath, content)

// Analyze gas optimizations
ast, _ := parser.ParseContract(filePath)
gasIssues, err := gasAnalyzer.AnalyzeGasOptimizations(filePath, content, ast)

// Get contract metrics
contractInfo, err := contractAnalyzer.AnalyzeContract(filePath, content)
```

## Vulnerability Rules

### ETH-001: Reentrancy Vulnerability
- **Severity**: Critical
- **Description**: External call followed by state change
- **Detection**: Pattern matching for call() followed by state modifications
- **Mitigation**: Use checks-effects-interactions pattern or ReentrancyGuard

### ETH-002: Integer Overflow/Underflow
- **Severity**: High
- **Description**: Arithmetic operations without overflow protection
- **Detection**: Checks for SafeMath usage or Solidity 0.8+ version
- **Mitigation**: Use SafeMath library or upgrade to Solidity 0.8+

### ETH-003: Unchecked External Call
- **Severity**: Medium
- **Description**: External call return value not checked
- **Detection**: Pattern matching for call() without require/assert
- **Mitigation**: Check return values with require() or handle failures

### ETH-004: Access Control Violation
- **Severity**: High
- **Description**: Missing or inadequate access control
- **Detection**: Public/external functions without access modifiers
- **Mitigation**: Add appropriate access control modifiers

### ETH-005: Timestamp Dependence
- **Severity**: Medium
- **Description**: Dangerous use of block.timestamp
- **Detection**: Pattern matching for timestamp comparisons
- **Mitigation**: Use block numbers or external oracles

### ETH-006: Denial of Service
- **Severity**: Medium
- **Description**: Potential DoS through gas limit or external dependency
- **Detection**: Unbounded loops over dynamic arrays
- **Mitigation**: Implement pagination or limit array sizes

### ETH-007: Front-Running Vulnerability
- **Severity**: Medium
- **Description**: Transaction ordering dependence
- **Detection**: Public price/rate setting functions
- **Mitigation**: Use commit-reveal schemes

### ETH-008: Uninitialized Storage Pointer
- **Severity**: High
- **Description**: Uninitialized storage pointer usage
- **Detection**: Storage variables without initialization
- **Mitigation**: Initialize storage pointers or use memory

### ETH-009: Delegatecall Injection
- **Severity**: Critical
- **Description**: Dangerous use of delegatecall
- **Detection**: Pattern matching for delegatecall usage
- **Mitigation**: Avoid delegatecall to user-controlled addresses

### ETH-010: Signature Malleability
- **Severity**: Medium
- **Description**: ECDSA signature malleability
- **Detection**: Raw ecrecover usage without validation
- **Mitigation**: Use OpenZeppelin's ECDSA library

## Gas Optimization Rules

### Storage Optimization
- **Cache storage reads**: Avoid multiple reads from same storage slot
- **Use memory for temporary data**: Prefer memory over storage for temporary variables
- **Pack structs efficiently**: Order struct fields to minimize storage slots

### Loop Optimization
- **Cache array length**: Store array.length in local variable before loops
- **Use pre-increment**: Prefer ++i over i++ in loops
- **Avoid unbounded loops**: Implement pagination for large datasets

### Function Optimization
- **Use external over public**: When functions aren't called internally
- **Mark variables constant**: Use constant keyword for unchanging values
- **Optimize event parameters**: Use indexed parameters for filtering

## Configuration

The Ethereum scanner can be configured through the main SPT configuration:

```json
{
  "security": {
    "rules": {
      "smart_contract_audit": {
        "enabled": true,
        "ethereum": {
          "reentrancy": true,
          "integer_overflow": true,
          "unchecked_calls": true,
          "access_control": true,
          "gas_optimization": true,
          "timestamp_dependence": true,
          "delegatecall_injection": true,
          "signature_malleability": true
        }
      }
    }
  }
}
```

## Testing

Run the comprehensive test suite:

```bash
cd backend/pkg/ethereum
go test -v
go test -bench=.
```

### Test Coverage
- Unit tests for all vulnerability detectors
- Integration tests for complete scanning workflow
- Benchmark tests for performance analysis
- Test contracts covering various vulnerability patterns

## Dependencies

### Required
- Go 1.21+
- github.com/sirupsen/logrus (logging)

### Optional
- `solc` (Solidity compiler) - for enhanced AST parsing
- When solc is unavailable, falls back to regex-based parsing

## Performance

### Benchmarks
- Single file scan: ~10-50ms (depending on contract size)
- Project scan: ~100-500ms (depending on number of files)
- AST parsing: ~5-20ms per file
- Vulnerability detection: ~20-100ms per file

### Optimization
- Concurrent file processing for project scans
- Efficient regex compilation and caching
- Minimal memory allocation during parsing
- Lazy evaluation of expensive operations

## Integration

### With SPT Backend
The Ethereum module integrates seamlessly with the SPT backend:

```go
// In scanner engine
ethereumScanner, err := ethereum.NewScanner(config)
engine.RegisterScanner("ethereum", ethereumScanner)
```

### With CLI Tool
```bash
# Scan Ethereum contracts
spt scan --chain ethereum --path ./contracts

# Ethereum-specific audit
spt audit contracts/ --chain ethereum
```

### With Web Dashboard
The module provides structured data that the web dashboard can display:
- Vulnerability summaries with severity levels
- Gas optimization recommendations
- Contract complexity metrics
- Interactive issue details

## Contributing

When adding new vulnerability detectors:

1. Add the rule to `vulnerability_detector.go`
2. Implement the detection function
3. Add comprehensive tests
4. Update documentation
5. Add example vulnerable/secure code

### Code Style
- Follow Go conventions
- Use descriptive variable names
- Add comprehensive comments
- Include error handling
- Write tests for all new functionality

## References

- [Ethereum Smart Contract Security Best Practices](https://consensys.github.io/smart-contract-best-practices/)
- [Solidity Documentation](https://docs.soliditylang.org/)
- [OpenZeppelin Contracts](https://docs.openzeppelin.com/contracts/)
- [SWC Registry](https://swcregistry.io/)
- [OWASP Smart Contract Top 10](https://owasp.org/www-project-smart-contract-top-10/)
