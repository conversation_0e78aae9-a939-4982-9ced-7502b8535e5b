package api

import (
	"net/http"
	"time"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/internal/scanner"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// <PERSON><PERSON> represents the API handler
type Handler struct {
	scanner *scanner.Engine
	config  *config.Config
	logger  *logrus.Logger
}

// NewHandler creates a new API handler
func NewHandler(scannerEngine *scanner.Engine, cfg *config.Config) *Handler {
	return &Handler{
		scanner: scannerEngine,
		config:  cfg,
		logger:  logrus.New(),
	}
}

// HealthCheck returns the health status of the service
func (h *Handler) HealthCheck(c *gin.Context) {
	health := models.HealthCheck{
		Status:    "healthy",
		Version:   "1.0.0",
		Timestamp: time.Now(),
		Services: map[string]string{
			"scanner": "healthy",
			"api":     "healthy",
		},
	}

	c.<PERSON>(http.StatusOK, health)
}

// StartScan initiates a new security scan
func (h *Handler) StartScan(c *gin.Context) {
	var req models.ScanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"details": err.Error(),
		})
		return
	}

	// Validate chains
	if len(req.Chains) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "At least one blockchain must be specified",
		})
		return
	}

	// Start scan asynchronously
	go func() {
		_, err := h.scanner.ScanProject(c.Request.Context(), req.ProjectPath, req.Chains)
		if err != nil {
			h.logger.Errorf("Scan failed: %v", err)
		}
	}()

	response := models.ScanResponse{
		ScanID:  generateScanID(),
		Status:  models.ScanStatusPending,
		Message: "Scan initiated successfully",
	}

	c.JSON(http.StatusAccepted, response)
}

// GetScanResult returns the result of a specific scan
func (h *Handler) GetScanResult(c *gin.Context) {
	scanID := c.Param("scanId")
	if scanID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Scan ID is required",
		})
		return
	}

	result, err := h.scanner.GetScanResult(scanID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Scan result not found",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetScanHistory returns the scan history
func (h *Handler) GetScanHistory(c *gin.Context) {
	history := h.scanner.GetScanHistory()
	c.JSON(http.StatusOK, gin.H{
		"scans": history,
		"total": len(history),
	})
}

// ScanFile scans a specific file
func (h *Handler) ScanFile(c *gin.Context) {
	filePath := c.Query("file")
	if filePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File path is required",
		})
		return
	}

	issues, err := h.scanner.ScanFile(c.Request.Context(), filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to scan file",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"file":   filePath,
		"issues": issues,
		"count":  len(issues),
	})
}

// GenerateReport generates a security report
func (h *Handler) GenerateReport(c *gin.Context) {
	var req models.ReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"details": err.Error(),
		})
		return
	}

	// Get scan result
	result, err := h.scanner.GetScanResult(req.ScanID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Scan result not found",
			"details": err.Error(),
		})
		return
	}

	// Generate report (placeholder implementation)
	report := models.SecurityReport{
		ID:           generateReportID(),
		ScanResultID: req.ScanID,
		ScanResult:   *result,
		Format:       req.Format,
		Content:      generateReportContent(result, req.Format),
		Summary: map[string]interface{}{
			"total_issues":    len(result.Issues),
			"critical_issues": result.SeverityCounts["critical"],
			"high_issues":     result.SeverityCounts["high"],
			"medium_issues":   result.SeverityCounts["medium"],
			"low_issues":      result.SeverityCounts["low"],
		},
		CreatedAt: time.Now(),
	}

	c.JSON(http.StatusOK, report)
}

// GetSecurityChecklist returns the security checklist
func (h *Handler) GetSecurityChecklist(c *gin.Context) {
	chain := c.Query("chain")

	// Generate checklist based on chain and configuration
	checklist := generateSecurityChecklist(chain, h.config)

	c.JSON(http.StatusOK, gin.H{
		"checklist": checklist,
		"total":     len(checklist),
	})
}

// GetConfiguration returns the current configuration
func (h *Handler) GetConfiguration(c *gin.Context) {
	c.JSON(http.StatusOK, h.config)
}

// UpdateConfiguration updates the configuration
func (h *Handler) UpdateConfiguration(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid configuration",
			"details": err.Error(),
		})
		return
	}

	// Validate configuration (placeholder)
	// In a real implementation, you'd validate the config and update it

	c.JSON(http.StatusOK, gin.H{
		"message": "Configuration updated successfully",
	})
}

// Helper functions

func generateScanID() string {
	return time.Now().Format("20060102150405") + "_scan"
}

func generateReportID() string {
	return time.Now().Format("20060102150405") + "_report"
}

func generateReportContent(result *models.ScanResult, format string) string {
	// Placeholder implementation
	switch format {
	case "markdown":
		return generateMarkdownReport(result)
	case "json":
		return generateJSONReport(result)
	default:
		return generateMarkdownReport(result)
	}
}

func generateMarkdownReport(result *models.ScanResult) string {
	// Placeholder markdown report generation
	return `# Security Scan Report

## Summary
- **Project**: ` + result.ProjectPath + `
- **Scan ID**: ` + result.ID + `
- **Duration**: ` + result.Duration.String() + `
- **Total Issues**: ` + string(rune(len(result.Issues))) + `

## Issues Found
(Issues would be listed here)

## Recommendations
(Security recommendations would be listed here)
`
}

func generateJSONReport(result *models.ScanResult) string {
	// Placeholder JSON report generation
	return `{"scan_id": "` + result.ID + `", "issues": ` + string(rune(len(result.Issues))) + `}`
}

func generateSecurityChecklist(chain string, cfg *config.Config) []models.SecurityChecklist {
	// Placeholder checklist generation
	checklist := []models.SecurityChecklist{
		{
			ID:          "check_1",
			Category:    "wallet",
			Chain:       chain,
			Title:       "Private Key Security",
			Description: "Ensure private keys are not exposed in code",
			Priority:    "critical",
			Status:      "pending",
			AutoCheck:   true,
		},
		{
			ID:          "check_2",
			Category:    "contract",
			Chain:       chain,
			Title:       "Reentrancy Protection",
			Description: "Check for reentrancy vulnerabilities",
			Priority:    "high",
			Status:      "pending",
			AutoCheck:   true,
		},
	}

	return checklist
}
