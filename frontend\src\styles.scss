/* You can add global styles to this file, and also import other style files */

@import '@angular/material/prebuilt-themes/indigo-pink.css';

html, body { 
  height: 100%; 
  margin: 0; 
  font-family: Roboto, "Helvetica Neue", sans-serif; 
}

/* Global utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.margin-bottom {
  margin-bottom: 20px;
}

.padding {
  padding: 20px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design customizations */
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

.mat-mdc-button {
  border-radius: 6px !important;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Security severity colors */
.severity-critical {
  color: #f44336 !important;
}

.severity-high {
  color: #ff9800 !important;
}

.severity-medium {
  color: #ffeb3b !important;
}

.severity-low {
  color: #4caf50 !important;
}

.severity-info {
  color: #2196f3 !important;
}

/* Chain-specific colors */
.chain-ethereum {
  color: #627eea !important;
}

.chain-bitcoin {
  color: #f7931a !important;
}

.chain-general {
  color: #666 !important;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mat-mdc-card {
    margin: 10px !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}
