package commands

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/config"
	"blockchain-spt/spt/backend/internal/models"
	"blockchain-spt/spt/backend/pkg/bitcoin"
	"blockchain-spt/spt/backend/pkg/dependencies"
	"blockchain-spt/spt/backend/pkg/ethereum"

	"github.com/spf13/cobra"
)

// ScanOptions represents scan command options
type ScanOptions struct {
	Path         string
	Chain        string
	Type         string
	Recursive    bool
	Include      []string
	Exclude      []string
	Severity     string
	Output       string
	Format       string
	Timeout      time.Duration
	Parallel     int
	FailOnIssues bool
}

// NewScanCommand creates the scan command
func NewScanCommand() *cobra.Command {
	opts := &ScanOptions{}

	cmd := &cobra.Command{
		Use:   "scan [path]",
		Short: "Scan files or directories for security issues",
		Long: `Scan files or directories for security vulnerabilities and misconfigurations.

The scan command performs comprehensive security analysis including:
- Smart contract vulnerabilities (Ethereum, Bitcoin)
- Dependency vulnerabilities and misconfigurations
- Environment and configuration security issues
- CI/CD pipeline security problems

Examples:
  spt scan ./project                           # Scan entire project
  spt scan --chain ethereum ./contracts       # Scan only Ethereum contracts
  spt scan --type dependencies ./package.json # Scan only dependencies
  spt scan --severity high ./src              # Show only high severity issues
  spt scan --output report.json --format json # Output JSON report`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			// Set path from argument or current directory
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}

			return runScan(cmd, opts)
		},
	}

	// Add flags
	cmd.Flags().StringVarP(&opts.Chain, "chain", "", "", "Blockchain to scan (ethereum, bitcoin, all)")
	cmd.Flags().StringVarP(&opts.Type, "type", "t", "", "Scan type (contracts, dependencies, environment, cicd, all)")
	cmd.Flags().BoolVarP(&opts.Recursive, "recursive", "r", true, "Scan directories recursively")
	cmd.Flags().StringSliceVar(&opts.Include, "include", []string{}, "File patterns to include")
	cmd.Flags().StringSliceVar(&opts.Exclude, "exclude", []string{}, "File patterns to exclude")
	cmd.Flags().StringVarP(&opts.Severity, "severity", "s", "", "Minimum severity level (low, medium, high, critical)")
	cmd.Flags().StringVarP(&opts.Output, "output", "o", "", "Output file path")
	cmd.Flags().StringVarP(&opts.Format, "format", "f", "table", "Output format (table, json, yaml, csv)")
	cmd.Flags().DurationVar(&opts.Timeout, "timeout", 5*time.Minute, "Scan timeout")
	cmd.Flags().IntVar(&opts.Parallel, "parallel", 4, "Number of parallel workers")
	cmd.Flags().BoolVar(&opts.FailOnIssues, "fail-on-issues", false, "Exit with non-zero code if issues found")

	return cmd
}

// runScan executes the scan command
func runScan(cmd *cobra.Command, opts *ScanOptions) error {
	// Load configuration
	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Apply command line options to config
	applyOptionsToConfig(cfg, opts)

	// Validate path
	if _, err := os.Stat(opts.Path); os.IsNotExist(err) {
		return fmt.Errorf("path does not exist: %s", opts.Path)
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), opts.Timeout)
	defer cancel()

	// Initialize scanners based on type
	scanners, err := initializeScanners(cfg, opts)
	if err != nil {
		return fmt.Errorf("failed to initialize scanners: %w", err)
	}

	// Perform scan
	fmt.Printf("🔍 Starting security scan of: %s\n", opts.Path)
	fmt.Printf("📊 Scan configuration:\n")
	fmt.Printf("   - Chain: %s\n", getChainDisplay(opts.Chain))
	fmt.Printf("   - Type: %s\n", getTypeDisplay(opts.Type))
	fmt.Printf("   - Severity: %s\n", getSeverityDisplay(opts.Severity))
	fmt.Printf("   - Recursive: %v\n", opts.Recursive)
	fmt.Printf("\n")

	startTime := time.Now()
	var allIssues []models.SecurityIssue

	// Scan with each enabled scanner
	for scannerType, scanner := range scanners {
		fmt.Printf("🔎 Running %s scanner...\n", scannerType)

		var issues []models.SecurityIssue
		var scanErr error

		// Determine if scanning file or directory
		fileInfo, err := os.Stat(opts.Path)
		if err != nil {
			return fmt.Errorf("failed to stat path: %w", err)
		}

		if fileInfo.IsDir() {
			issues, scanErr = scanDirectory(ctx, scanner, opts.Path, opts)
		} else {
			issues, scanErr = scanFile(ctx, scanner, opts.Path)
		}

		if scanErr != nil {
			fmt.Printf("⚠️  Warning: %s scanner failed: %v\n", scannerType, scanErr)
			continue
		}

		// Filter issues by severity
		filteredIssues := filterIssuesBySeverity(issues, opts.Severity)
		allIssues = append(allIssues, filteredIssues...)

		fmt.Printf("   Found %d issues (%d after filtering)\n", len(issues), len(filteredIssues))
	}

	duration := time.Since(startTime)
	fmt.Printf("\n✅ Scan completed in %v\n", duration)

	// Display results
	if err := displayResults(allIssues, opts); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Save output if specified
	if opts.Output != "" {
		if err := saveResults(allIssues, opts); err != nil {
			return fmt.Errorf("failed to save results: %w", err)
		}
		fmt.Printf("📄 Results saved to: %s\n", opts.Output)
	}

	// Exit with error code if issues found and fail-on-issues is set
	if opts.FailOnIssues && len(allIssues) > 0 {
		fmt.Printf("\n❌ Exiting with error code due to security issues found\n")
		os.Exit(1)
	}

	return nil
}

// initializeScanners creates and configures scanners based on options
func initializeScanners(cfg *config.Config, opts *ScanOptions) (map[string]interface{}, error) {
	scanners := make(map[string]interface{})

	// Determine which scanners to initialize
	enableEthereum := opts.Chain == "" || opts.Chain == "ethereum" || opts.Chain == "all"
	enableBitcoin := opts.Chain == "" || opts.Chain == "bitcoin" || opts.Chain == "all"
	enableDependencies := opts.Type == "" || opts.Type == "dependencies" || opts.Type == "all"
	enableContracts := opts.Type == "" || opts.Type == "contracts" || opts.Type == "all"

	// Initialize Ethereum scanner
	if enableEthereum && enableContracts {
		ethScanner, err := ethereum.NewScanner(cfg)
		if err != nil {
			return nil, fmt.Errorf("failed to create Ethereum scanner: %w", err)
		}
		scanners["ethereum"] = ethScanner
	}

	// Initialize Bitcoin scanner
	if enableBitcoin && enableContracts {
		btcScanner, err := bitcoin.NewScanner(cfg)
		if err != nil {
			return nil, fmt.Errorf("failed to create Bitcoin scanner: %w", err)
		}
		scanners["bitcoin"] = btcScanner
	}

	// Initialize Dependencies scanner
	if enableDependencies {
		depsScanner, err := dependencies.NewScanner(cfg)
		if err != nil {
			return nil, fmt.Errorf("failed to create Dependencies scanner: %w", err)
		}
		scanners["dependencies"] = depsScanner
	}

	if len(scanners) == 0 {
		return nil, fmt.Errorf("no scanners enabled with current options")
	}

	return scanners, nil
}

// scanDirectory scans all files in a directory
func scanDirectory(ctx context.Context, scanner interface{}, dirPath string, opts *ScanOptions) ([]models.SecurityIssue, error) {
	var allIssues []models.SecurityIssue

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Check if file should be excluded
		if shouldExcludeFile(path, opts) {
			return nil
		}

		// Check if file should be included
		if !shouldIncludeFile(path, opts) {
			return nil
		}

		// Scan the file
		issues, err := scanFile(ctx, scanner, path)
		if err != nil {
			// Log warning but continue with other files
			fmt.Printf("   Warning: failed to scan %s: %v\n", path, err)
			return nil
		}

		allIssues = append(allIssues, issues...)
		return nil
	})

	return allIssues, err
}

// scanFile scans a single file with the appropriate scanner
func scanFile(ctx context.Context, scanner interface{}, filePath string) ([]models.SecurityIssue, error) {
	switch s := scanner.(type) {
	case *ethereum.Scanner:
		return s.ScanFile(ctx, filePath)
	case *bitcoin.Scanner:
		return s.ScanFile(ctx, filePath)
	case *dependencies.Scanner:
		return s.ScanFile(ctx, filePath)
	default:
		return nil, fmt.Errorf("unknown scanner type: %T", scanner)
	}
}

// Helper functions for display and filtering
func getChainDisplay(chain string) string {
	if chain == "" {
		return "all"
	}
	return chain
}

func getTypeDisplay(scanType string) string {
	if scanType == "" {
		return "all"
	}
	return scanType
}

func getSeverityDisplay(severity string) string {
	if severity == "" {
		return "all"
	}
	return severity
}

func shouldExcludeFile(path string, opts *ScanOptions) bool {
	for _, pattern := range opts.Exclude {
		if matched, _ := filepath.Match(pattern, filepath.Base(path)); matched {
			return true
		}
		if strings.Contains(path, pattern) {
			return true
		}
	}
	return false
}

func shouldIncludeFile(path string, opts *ScanOptions) bool {
	if len(opts.Include) == 0 {
		return true
	}

	for _, pattern := range opts.Include {
		if matched, _ := filepath.Match(pattern, filepath.Base(path)); matched {
			return true
		}
		if strings.Contains(path, pattern) {
			return true
		}
	}
	return false
}

func filterIssuesBySeverity(issues []models.SecurityIssue, minSeverity string) []models.SecurityIssue {
	if minSeverity == "" {
		return issues
	}

	severityLevels := map[string]int{
		"low":      1,
		"medium":   2,
		"high":     3,
		"critical": 4,
	}

	minLevel, exists := severityLevels[strings.ToLower(minSeverity)]
	if !exists {
		return issues
	}

	var filtered []models.SecurityIssue
	for _, issue := range issues {
		if level, exists := severityLevels[strings.ToLower(issue.Severity)]; exists && level >= minLevel {
			filtered = append(filtered, issue)
		}
	}

	return filtered
}
