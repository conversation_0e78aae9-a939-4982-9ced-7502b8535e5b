# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# Build directories
backend/bin/
cli/bin/
frontend/dist/
release/

# Dependency directories
node_modules/
vendor/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Database files
*.db
*.sqlite
*.sqlite3

# Security sensitive files
*.key
*.pem
*.p12
*.pfx
wallet.dat
keystore/
private_keys/

# Temporary files
tmp/
temp/
.tmp/

# Angular specific
frontend/.angular/

# Compiled output
frontend/dist/
frontend/tmp/
frontend/out-tsc/

# Only exists if Bazel was run
frontend/bazel-out

# profiling files
frontend/chrome-profiler-events*.json

# Backup files
*.bak
*.backup

# Local configuration overrides
config.local.json
spt.local.config.json

# Documentation build
docs/_build/
docs/.doctrees/

# Python cache (if any Python tools are used)
__pycache__/
*.py[cod]
*$py.class

# Docker
.dockerignore

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes
*.kubeconfig

# Local development
.local/
local/
