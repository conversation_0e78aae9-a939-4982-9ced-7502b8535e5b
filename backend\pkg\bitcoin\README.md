# Bitcoin Security Module

## Overview

The Bitcoin Security Module provides comprehensive security analysis for Bitcoin-related code, including wallet implementations, script analysis, UTXO management, and transaction security. It covers both Bitcoin Core implementations and application-level Bitcoin integrations.

## Features

### 🔍 Advanced Security Analysis
- **Private Key Exposure**: Detects exposed private keys in various formats (WIF, hex)
- **Wallet Security**: Analyzes wallet implementations and configurations
- **Script Analysis**: Validates Bitcoin scripts and detects dangerous opcodes
- **UTXO Management**: Checks UTXO handling patterns and transaction security
- **Multisig Security**: Validates multisig configurations and implementations
- **Network Security**: Analyzes RPC configurations and network settings

### 🔐 Script Security Analysis
- **Opcode Validation**: Identifies deprecated and dangerous opcodes
- **Script Type Detection**: Recognizes P2PKH, P2SH, P2WPKH, P2WSH, P2TR, Multisig
- **Standard Compliance**: Validates scripts against Bitcoin standards
- **Multisig Analysis**: Checks multisig threshold configurations
- **<PERSON><PERSON>t Pattern Recognition**: Detects common script vulnerabilities

### 💰 UTXO & Transaction Analysis
- **Dust Output Detection**: Identifies uneconomical outputs
- **UTXO Fragmentation**: Analyzes wallet UTXO distribution
- **Fee Analysis**: Detects unusually high transaction fees
- **Confirmation Checks**: Validates proper confirmation handling
- **RBF Detection**: Identifies Replace-By-Fee enabled transactions
- **Transaction Pattern Analysis**: Checks for security anti-patterns

### 🔑 Wallet Security Assessment
- **Key Management**: Analyzes private key storage and handling
- **Address Security**: Validates address usage patterns
- **Seed Security**: Detects exposed seed phrases and mnemonics
- **HD Wallet Analysis**: Checks hierarchical deterministic wallet implementations
- **Encryption Validation**: Verifies wallet encryption practices
- **Backup Assessment**: Evaluates backup and recovery mechanisms

## Architecture

```
bitcoin/
├── scanner.go           # Main scanner interface
├── script_analyzer.go   # Bitcoin script analysis
├── utxo_analyzer.go     # UTXO and transaction analysis
├── wallet_analyzer.go   # Wallet security analysis
└── scanner_test.go      # Comprehensive test suite
```

## Usage

### Basic Scanning

```go
import "blockchain-spt/spt/backend/pkg/bitcoin"

// Create scanner
scanner, err := bitcoin.NewScanner(config)
if err != nil {
    log.Fatal(err)
}

// Scan a single file
issues, err := scanner.ScanFile(ctx, "wallet/bitcoin.js")
if err != nil {
    log.Fatal(err)
}

// Scan entire project
issues, err := scanner.ScanProject(ctx, "./bitcoin-wallet")
if err != nil {
    log.Fatal(err)
}
```

### Advanced Analysis

```go
// Create specialized analyzers
scriptAnalyzer := bitcoin.NewScriptAnalyzer()
utxoAnalyzer := bitcoin.NewUTXOAnalyzer()
walletAnalyzer := bitcoin.NewWalletAnalyzer()

// Analyze Bitcoin scripts
scriptIssues, err := scriptAnalyzer.AnalyzeScript(filePath, content)

// Analyze UTXO patterns
utxoIssues, err := utxoAnalyzer.AnalyzeUTXOs(filePath, content)

// Analyze wallet security
walletIssues, err := walletAnalyzer.AnalyzeWallet(filePath, content)
```

## Security Rules

### BTC-001: Private Key Exposure
- **Severity**: Critical
- **Description**: Bitcoin private key found in source code
- **Detection**: Pattern matching for WIF and hex private keys
- **Mitigation**: Use secure key management and environment variables

### BTC-002: Seed Phrase Exposure
- **Severity**: Critical
- **Description**: Bitcoin seed phrase or mnemonic found in code
- **Detection**: Pattern matching for BIP39 mnemonic phrases
- **Mitigation**: Store seeds securely and never commit to version control

### BTC-003: Hardcoded Wallet Password
- **Severity**: High
- **Description**: Wallet password hardcoded in source code
- **Detection**: Pattern matching for password assignments
- **Mitigation**: Use environment variables or secure configuration

### BTC-004: Insecure Script
- **Severity**: High
- **Description**: Bitcoin script uses deprecated or dangerous opcodes
- **Detection**: Opcode analysis and validation
- **Mitigation**: Use only standard, secure opcodes

### BTC-005: Weak Multisig Configuration
- **Severity**: Medium
- **Description**: Multisig configuration provides insufficient security
- **Detection**: Analysis of M-of-N threshold values
- **Mitigation**: Use appropriate multisig thresholds (e.g., 2-of-3, 3-of-5)

### BTC-006: Non-Standard Script
- **Severity**: Medium
- **Description**: Script doesn't follow Bitcoin standard patterns
- **Detection**: Script type classification and validation
- **Mitigation**: Use standard script types for better compatibility

### BTC-007: Dust Outputs
- **Severity**: Low
- **Description**: Transaction outputs below dust threshold
- **Detection**: Value analysis against dust threshold (546 satoshis)
- **Mitigation**: Consolidate small outputs or avoid creating dust

### BTC-008: UTXO Fragmentation
- **Severity**: Medium
- **Description**: Excessive number of UTXOs leading to high fees
- **Detection**: UTXO count analysis
- **Mitigation**: Consolidate UTXOs during low fee periods

### BTC-009: High Transaction Fee
- **Severity**: Low
- **Description**: Transaction fee rate is unusually high
- **Detection**: Fee rate calculation (satoshis per byte)
- **Mitigation**: Review fee calculation algorithms

### BTC-010: Unconfirmed UTXO Usage
- **Severity**: Medium
- **Description**: Using UTXOs without sufficient confirmations
- **Detection**: Confirmation count analysis
- **Mitigation**: Wait for adequate confirmations before spending

### BTC-011: Address Reuse
- **Severity**: Medium
- **Description**: Potential Bitcoin address reuse affecting privacy
- **Detection**: Multiple address usage patterns
- **Mitigation**: Generate new addresses for each transaction

### BTC-012: Testnet Address in Production
- **Severity**: Medium
- **Description**: Testnet address found in production code
- **Detection**: Address prefix analysis
- **Mitigation**: Use mainnet addresses for production

### BTC-013: Insecure RPC Configuration
- **Severity**: Medium
- **Description**: Bitcoin RPC credentials exposed in code
- **Detection**: Pattern matching for RPC configuration
- **Mitigation**: Use secure configuration files with proper permissions

### BTC-014: Hardcoded Transaction ID
- **Severity**: Low
- **Description**: Transaction ID hardcoded in source code
- **Detection**: Pattern matching for 64-character hex strings
- **Mitigation**: Use dynamic transaction lookup

### BTC-015: RBF Enabled Transaction
- **Severity**: Low
- **Description**: Transaction has Replace-By-Fee enabled
- **Detection**: Sequence number analysis
- **Mitigation**: Be aware of RBF implications for transaction finality

## Script Analysis

### Supported Script Types
- **P2PK**: Pay to Public Key
- **P2PKH**: Pay to Public Key Hash (Legacy addresses)
- **P2SH**: Pay to Script Hash
- **P2WPKH**: Pay to Witness Public Key Hash (SegWit)
- **P2WSH**: Pay to Witness Script Hash (SegWit)
- **P2TR**: Pay to Taproot
- **Multisig**: M-of-N multisignature scripts
- **OP_RETURN**: Null data scripts

### Dangerous Opcodes Detected
- **OP_CAT**: String concatenation (disabled)
- **OP_SUBSTR**: String operations (disabled)
- **OP_LEFT/RIGHT**: String operations (disabled)
- **OP_INVERT**: Bitwise operations (disabled)
- **OP_AND/OR/XOR**: Bitwise operations (disabled)
- **OP_2MUL/2DIV**: Arithmetic operations (disabled)
- **OP_MUL/DIV/MOD**: Arithmetic operations (disabled)
- **OP_LSHIFT/RSHIFT**: Bit shifting (disabled)

## UTXO Analysis

### Metrics Analyzed
- **Dust Threshold**: 546 satoshis (standard)
- **Large Output Threshold**: 1 BTC (100,000,000 satoshis)
- **Fragmentation Threshold**: 100+ UTXOs
- **High Fee Threshold**: 100+ satoshis per byte

### Transaction Analysis
- **Fee Rate Calculation**: Satoshis per byte
- **RBF Detection**: Sequence number analysis
- **Version Validation**: Transaction version checks
- **Locktime Analysis**: Time-based transaction locks

## Wallet Security

### Key Formats Detected
- **WIF**: Wallet Import Format (Base58Check)
- **Compressed WIF**: 52-character WIF keys
- **Hex**: 64-character hexadecimal private keys
- **Public Keys**: Compressed (66 chars) and uncompressed (130 chars)

### Address Types Supported
- **Legacy**: P2PKH addresses starting with '1'
- **Script**: P2SH addresses starting with '3'
- **SegWit**: Bech32 addresses starting with 'bc1'
- **Testnet**: Addresses starting with 'm', 'n', '2', 'tb1'

### Mnemonic Validation
- **BIP39**: 12, 15, 18, 21, or 24-word phrases
- **Word Count**: Validates proper mnemonic length
- **Pattern Recognition**: Identifies mnemonic-like phrases

## Configuration

The Bitcoin scanner can be configured through the main SPT configuration:

```json
{
  "security": {
    "rules": {
      "private_key_exposure": {
        "enabled": true,
        "bitcoin": {
          "private_keys": true,
          "addresses": true,
          "seeds": true,
          "rpc": true
        }
      }
    }
  }
}
```

## Testing

Run the comprehensive test suite:

```bash
cd backend/pkg/bitcoin
go test -v
go test -bench=.
```

### Test Coverage
- Unit tests for all analyzers
- Integration tests for complete scanning workflow
- Benchmark tests for performance analysis
- Test samples covering various Bitcoin patterns

## Performance

### Benchmarks
- Single file scan: ~5-25ms (depending on file size)
- Project scan: ~50-200ms (depending on number of files)
- Script analysis: ~1-10ms per script
- Wallet analysis: ~5-20ms per file

### Optimization
- Efficient regex compilation and caching
- Parallel file processing for project scans
- Minimal memory allocation during analysis
- Fast pattern matching algorithms

## Integration

### With SPT Backend
The Bitcoin module integrates seamlessly with the SPT backend:

```go
// In scanner engine
bitcoinScanner, err := bitcoin.NewScanner(config)
engine.RegisterScanner("bitcoin", bitcoinScanner)
```

### With CLI Tool
```bash
# Scan Bitcoin code
spt scan --chain bitcoin --path ./bitcoin-wallet

# Bitcoin-specific checks
spt check-keys --chain bitcoin
spt audit wallet/ --chain bitcoin
```

### With Web Dashboard
The module provides structured data for the web dashboard:
- Security issue summaries with severity levels
- Script analysis results with opcode details
- UTXO management recommendations
- Wallet security assessments

## Best Practices

### Secure Development
1. **Never commit private keys** to version control
2. **Use environment variables** for sensitive configuration
3. **Implement proper key management** with hardware security modules
4. **Validate all inputs** including addresses and transaction data
5. **Use standard script types** for maximum compatibility
6. **Implement proper confirmation checks** before spending UTXOs

### Code Organization
1. **Separate concerns** between wallet, transaction, and script logic
2. **Use configuration files** for network-specific settings
3. **Implement comprehensive logging** for security events
4. **Add input validation** at all API boundaries
5. **Use type-safe languages** when possible

## Contributing

When adding new security rules:

1. Add the rule to the appropriate analyzer
2. Implement the detection function
3. Add comprehensive tests
4. Update documentation
5. Add example vulnerable/secure code

### Code Style
- Follow Go conventions
- Use descriptive variable names
- Add comprehensive comments
- Include error handling
- Write tests for all new functionality

## References

- [Bitcoin Developer Guide](https://bitcoin.org/en/developer-guide)
- [Bitcoin Script Reference](https://en.bitcoin.it/wiki/Script)
- [BIP32: Hierarchical Deterministic Wallets](https://github.com/bitcoin/bips/blob/master/bip-0032.mediawiki)
- [BIP39: Mnemonic Code](https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki)
- [Bitcoin Security Best Practices](https://bitcoin.org/en/secure-your-wallet)
