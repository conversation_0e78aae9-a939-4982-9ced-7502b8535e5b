package models

import (
	"time"
)

// ScanStatus represents the status of a security scan
type ScanStatus string

const (
	ScanStatusPending   ScanStatus = "pending"
	ScanStatusRunning   ScanStatus = "running"
	ScanStatusCompleted ScanStatus = "completed"
	ScanStatusFailed    ScanStatus = "failed"
	ScanStatusCancelled ScanStatus = "cancelled"
)

// SecurityIssue represents a security vulnerability or issue
type SecurityIssue struct {
	ID          string            `json:"id" gorm:"primaryKey"`
	Type        string            `json:"type"`        // e.g., "key_leak", "reentrancy", "overflow"
	Severity    string            `json:"severity"`    // critical, high, medium, low, info
	Title       string            `json:"title"`
	Description string            `json:"description"`
	File        string            `json:"file"`
	Line        int               `json:"line"`
	Column      int               `json:"column"`
	Code        string            `json:"code"`        // The problematic code snippet
	Chain       string            `json:"chain"`       // ethereum, bitcoin, general
	Category    string            `json:"category"`    // smart_contract, wallet, environment, dependency
	CWE         string            `json:"cwe"`         // Common Weakness Enumeration ID
	OWASP       string            `json:"owasp"`       // OWASP category
	References  []string          `json:"references"`  // Links to documentation/fixes
	Suggestion  string            `json:"suggestion"`  // How to fix the issue
	Metadata    map[string]string `json:"metadata"`    // Additional context
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// ScanResult represents the result of a security scan
type ScanResult struct {
	ID              string                 `json:"id" gorm:"primaryKey"`
	ProjectPath     string                 `json:"project_path"`
	Chains          []string               `json:"chains" gorm:"serializer:json"`
	Status          ScanStatus             `json:"status"`
	StartTime       time.Time              `json:"start_time"`
	EndTime         time.Time              `json:"end_time"`
	Duration        time.Duration          `json:"duration"`
	Issues          []SecurityIssue        `json:"issues" gorm:"foreignKey:ScanResultID"`
	SeverityCounts  map[string]int         `json:"severity_counts" gorm:"serializer:json"`
	FilesScanned    int                    `json:"files_scanned"`
	LinesScanned    int                    `json:"lines_scanned"`
	Error           string                 `json:"error,omitempty"`
	Configuration   map[string]interface{} `json:"configuration" gorm:"serializer:json"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// WalletInfo represents wallet-related information
type WalletInfo struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Address     string    `json:"address"`
	Chain       string    `json:"chain"`
	Type        string    `json:"type"`        // private_key, mnemonic, keystore
	File        string    `json:"file"`
	Line        int       `json:"line"`
	IsExposed   bool      `json:"is_exposed"`
	RiskLevel   string    `json:"risk_level"`  // critical, high, medium, low
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ContractInfo represents smart contract information
type ContractInfo struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	Name         string    `json:"name"`
	File         string    `json:"file"`
	Chain        string    `json:"chain"`
	Language     string    `json:"language"`     // solidity, vyper, etc.
	Version      string    `json:"version"`
	Functions    []string  `json:"functions" gorm:"serializer:json"`
	Events       []string  `json:"events" gorm:"serializer:json"`
	Modifiers    []string  `json:"modifiers" gorm:"serializer:json"`
	Dependencies []string  `json:"dependencies" gorm:"serializer:json"`
	GasUsage     int64     `json:"gas_usage"`
	Complexity   int       `json:"complexity"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// DependencyInfo represents dependency information
type DependencyInfo struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	Name            string    `json:"name"`
	Version         string    `json:"version"`
	LatestVersion   string    `json:"latest_version"`
	Type            string    `json:"type"`            // npm, pip, cargo, etc.
	File            string    `json:"file"`
	HasVulnerability bool     `json:"has_vulnerability"`
	Vulnerabilities []string  `json:"vulnerabilities" gorm:"serializer:json"`
	RiskScore       float64   `json:"risk_score"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// SecurityReport represents a generated security report
type SecurityReport struct {
	ID           string                 `json:"id" gorm:"primaryKey"`
	ScanResultID string                 `json:"scan_result_id"`
	ScanResult   ScanResult             `json:"scan_result" gorm:"foreignKey:ScanResultID"`
	Format       string                 `json:"format"`       // markdown, pdf, json
	Content      string                 `json:"content"`
	FilePath     string                 `json:"file_path"`
	Summary      map[string]interface{} `json:"summary" gorm:"serializer:json"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// SecurityChecklist represents a security checklist item
type SecurityChecklist struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Category    string    `json:"category"`    // wallet, contract, environment, deployment
	Chain       string    `json:"chain"`       // ethereum, bitcoin, general
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Priority    string    `json:"priority"`    // critical, high, medium, low
	Status      string    `json:"status"`      // pending, completed, skipped, failed
	AutoCheck   bool      `json:"auto_check"`  // Can be automatically verified
	References  []string  `json:"references" gorm:"serializer:json"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ProjectConfig represents project-specific configuration
type ProjectConfig struct {
	ID            string                 `json:"id" gorm:"primaryKey"`
	ProjectPath   string                 `json:"project_path"`
	Name          string                 `json:"name"`
	Chains        []string               `json:"chains" gorm:"serializer:json"`
	ScanPaths     []string               `json:"scan_paths" gorm:"serializer:json"`
	ExcludePaths  []string               `json:"exclude_paths" gorm:"serializer:json"`
	SecurityLevel string                 `json:"security_level"`
	Rules         map[string]interface{} `json:"rules" gorm:"serializer:json"`
	LastScanID    string                 `json:"last_scan_id"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// ScanRequest represents a scan request
type ScanRequest struct {
	ProjectPath string   `json:"project_path" binding:"required"`
	Chains      []string `json:"chains" binding:"required"`
	ScanType    string   `json:"scan_type"`    // full, quick, file
	FilePath    string   `json:"file_path"`    // For file-specific scans
	Options     map[string]interface{} `json:"options"`
}

// ScanResponse represents a scan response
type ScanResponse struct {
	ScanID  string     `json:"scan_id"`
	Status  ScanStatus `json:"status"`
	Message string     `json:"message"`
}

// ReportRequest represents a report generation request
type ReportRequest struct {
	ScanID     string `json:"scan_id" binding:"required"`
	Format     string `json:"format"`     // markdown, pdf, json
	OutputPath string `json:"output_path"`
	Sections   []string `json:"sections"`
}

// HealthCheck represents system health status
type HealthCheck struct {
	Status    string            `json:"status"`
	Version   string            `json:"version"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}
