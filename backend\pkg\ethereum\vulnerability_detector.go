package ethereum

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/spt/backend/internal/models"

	"github.com/sirupsen/logrus"
)

// VulnerabilityDetector handles advanced vulnerability detection
type VulnerabilityDetector struct {
	parser *ASTParser
	logger *logrus.Logger
}

// VulnerabilityRule represents a vulnerability detection rule
type VulnerabilityRule struct {
	ID          string
	Name        string
	Description string
	Severity    string
	Category    string
	CWE         string
	OWASP       string
	Detector    func(*ContractAST, string) []models.SecurityIssue
}

// NewVulnerabilityDetector creates a new vulnerability detector
func NewVulnerabilityDetector() *VulnerabilityDetector {
	return &VulnerabilityDetector{
		parser: NewASTParser(),
		logger: logrus.New(),
	}
}

// DetectVulnerabilities performs comprehensive vulnerability detection
func (vd *VulnerabilityDetector) DetectVulnerabilities(filePath string, content string) ([]models.SecurityIssue, error) {
	var allIssues []models.SecurityIssue

	// Parse the contract AST
	ast, err := vd.parser.ParseContract(filePath)
	if err != nil {
		vd.logger.Warnf("Failed to parse AST for %s, using fallback detection: %v", filePath, err)
		// Fallback to regex-based detection
		return vd.detectWithRegex(filePath, content)
	}

	// Apply all vulnerability rules
	rules := vd.getVulnerabilityRules()
	for _, rule := range rules {
		issues := rule.Detector(ast, content)
		for i := range issues {
			issues[i].File = filePath
			issues[i].Chain = "ethereum"
			issues[i].CreatedAt = time.Now()
			issues[i].UpdatedAt = time.Now()
		}
		allIssues = append(allIssues, issues...)
	}

	return allIssues, nil
}

// getVulnerabilityRules returns all vulnerability detection rules
func (vd *VulnerabilityDetector) getVulnerabilityRules() []VulnerabilityRule {
	return []VulnerabilityRule{
		{
			ID:          "ETH-001",
			Name:        "Reentrancy Vulnerability",
			Description: "External call followed by state change",
			Severity:    "critical",
			Category:    "smart_contract",
			CWE:         "CWE-362",
			OWASP:       "A06:2021",
			Detector:    vd.detectReentrancy,
		},
		{
			ID:          "ETH-002",
			Name:        "Integer Overflow/Underflow",
			Description: "Arithmetic operations without overflow protection",
			Severity:    "high",
			Category:    "smart_contract",
			CWE:         "CWE-190",
			OWASP:       "A03:2021",
			Detector:    vd.detectIntegerOverflow,
		},
		{
			ID:          "ETH-003",
			Name:        "Unchecked External Call",
			Description: "External call return value not checked",
			Severity:    "medium",
			Category:    "smart_contract",
			CWE:         "CWE-252",
			OWASP:       "A06:2021",
			Detector:    vd.detectUncheckedCalls,
		},
		{
			ID:          "ETH-004",
			Name:        "Access Control Violation",
			Description: "Missing or inadequate access control",
			Severity:    "high",
			Category:    "smart_contract",
			CWE:         "CWE-284",
			OWASP:       "A01:2021",
			Detector:    vd.detectAccessControl,
		},
		{
			ID:          "ETH-005",
			Name:        "Timestamp Dependence",
			Description: "Dangerous use of block.timestamp",
			Severity:    "medium",
			Category:    "smart_contract",
			CWE:         "CWE-829",
			OWASP:       "A06:2021",
			Detector:    vd.detectTimestampDependence,
		},
		{
			ID:          "ETH-006",
			Name:        "Denial of Service",
			Description: "Potential DoS through gas limit or external dependency",
			Severity:    "medium",
			Category:    "smart_contract",
			CWE:         "CWE-400",
			OWASP:       "A06:2021",
			Detector:    vd.detectDoS,
		},
		{
			ID:          "ETH-007",
			Name:        "Front-Running Vulnerability",
			Description: "Transaction ordering dependence",
			Severity:    "medium",
			Category:    "smart_contract",
			CWE:         "CWE-362",
			OWASP:       "A06:2021",
			Detector:    vd.detectFrontRunning,
		},
		{
			ID:          "ETH-008",
			Name:        "Uninitialized Storage Pointer",
			Description: "Uninitialized storage pointer usage",
			Severity:    "high",
			Category:    "smart_contract",
			CWE:         "CWE-824",
			OWASP:       "A06:2021",
			Detector:    vd.detectUninitializedStorage,
		},
		{
			ID:          "ETH-009",
			Name:        "Delegatecall Injection",
			Description: "Dangerous use of delegatecall",
			Severity:    "critical",
			Category:    "smart_contract",
			CWE:         "CWE-470",
			OWASP:       "A03:2021",
			Detector:    vd.detectDelegatecallInjection,
		},
		{
			ID:          "ETH-010",
			Name:        "Signature Malleability",
			Description: "ECDSA signature malleability",
			Severity:    "medium",
			Category:    "smart_contract",
			CWE:         "CWE-347",
			OWASP:       "A02:2021",
			Detector:    vd.detectSignatureMalleability,
		},
	}
}

// detectReentrancy detects reentrancy vulnerabilities
func (vd *VulnerabilityDetector) detectReentrancy(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Look for external calls followed by state changes
	lines := strings.Split(content, "\n")
	externalCallPattern := regexp.MustCompile(`(?i)(\.call\(|\.send\(|\.transfer\(|\.delegatecall\()`)
	stateChangePattern := regexp.MustCompile(`(?i)(=\s*[^=]|balances\[|\.push\(|\.pop\(|delete\s+)`)

	for i, line := range lines {
		if externalCallPattern.MatchString(line) {
			// Check subsequent lines for state changes
			for j := i + 1; j < len(lines) && j < i+10; j++ {
				if stateChangePattern.MatchString(lines[j]) {
					issues = append(issues, models.SecurityIssue{
						ID:          fmt.Sprintf("reentrancy_%d", i+1),
						Type:        "reentrancy",
						Severity:    "critical",
						Title:       "Potential Reentrancy Vulnerability",
						Description: "External call followed by state change may be vulnerable to reentrancy attacks",
						Line:        i + 1,
						Code:        strings.TrimSpace(line),
						Category:    "smart_contract",
						CWE:         "CWE-362",
						OWASP:       "A06:2021",
						Suggestion:  "Use the checks-effects-interactions pattern or reentrancy guards (ReentrancyGuard)",
						References:  []string{"https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/"},
					})
					break
				}
			}
		}
	}

	return issues
}

// detectIntegerOverflow detects integer overflow vulnerabilities
func (vd *VulnerabilityDetector) detectIntegerOverflow(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check Solidity version first
	hasSafeMath := strings.Contains(content, "SafeMath") || strings.Contains(content, "using SafeMath")
	isModernSolidity := false

	for _, pragma := range ast.Pragmas {
		if pragma.Name == "solidity" {
			// Check if version is 0.8.0 or higher (has built-in overflow protection)
			if strings.Contains(pragma.Value, "^0.8") || strings.Contains(pragma.Value, ">=0.8") {
				isModernSolidity = true
			}
		}
	}

	if !hasSafeMath && !isModernSolidity {
		lines := strings.Split(content, "\n")
		arithmeticPattern := regexp.MustCompile(`(?i)(\w+\s*[\+\-\*\/]\s*\w+|\w+\s*[\+\-]\+|\w+\s*\*\*\s*\w+)`)

		for i, line := range lines {
			if arithmeticPattern.MatchString(line) && !strings.Contains(line, "//") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("overflow_%d", i+1),
					Type:        "integer_overflow",
					Severity:    "high",
					Title:       "Potential Integer Overflow/Underflow",
					Description: "Arithmetic operation without overflow protection",
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Category:    "smart_contract",
					CWE:         "CWE-190",
					Suggestion:  "Use SafeMath library or upgrade to Solidity 0.8+ for built-in overflow protection",
					References:  []string{"https://docs.openzeppelin.com/contracts/2.x/api/math#SafeMath"},
				})
			}
		}
	}

	return issues
}

// detectUncheckedCalls detects unchecked external calls
func (vd *VulnerabilityDetector) detectUncheckedCalls(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	callPattern := regexp.MustCompile(`(?i)(\w+\.call\(|\w+\.send\()`)

	for i, line := range lines {
		if callPattern.MatchString(line) {
			// Check if return value is checked
			if !strings.Contains(line, "require(") && !strings.Contains(line, "assert(") &&
				!strings.Contains(line, "if(") && !strings.Contains(line, "bool") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("unchecked_call_%d", i+1),
					Type:        "unchecked_call",
					Severity:    "medium",
					Title:       "Unchecked External Call",
					Description: "External call return value is not checked",
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Category:    "smart_contract",
					CWE:         "CWE-252",
					Suggestion:  "Check the return value of external calls using require() or handle the failure case",
					References:  []string{"https://consensys.github.io/smart-contract-best-practices/recommendations/#handle-errors-in-external-calls"},
				})
			}
		}
	}

	return issues
}

// detectAccessControl detects access control issues
func (vd *VulnerabilityDetector) detectAccessControl(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	for _, function := range ast.Functions {
		if function.Visibility == "public" || function.Visibility == "external" {
			// Check if function has access control
			hasAccessControl := false
			for _, modifier := range function.Modifiers {
				if strings.Contains(modifier, "only") || strings.Contains(modifier, "auth") ||
					strings.Contains(modifier, "require") {
					hasAccessControl = true
					break
				}
			}

			// Check for state-changing functions without access control
			if !hasAccessControl && (function.StateMutability == "" || function.StateMutability == "nonpayable") {
				// Check if function body contains state changes
				if vd.containsStateChanges(content, function.Location.Line) {
					issues = append(issues, models.SecurityIssue{
						ID:          fmt.Sprintf("access_control_%s", function.Name),
						Type:        "access_control",
						Severity:    "high",
						Title:       "Missing Access Control",
						Description: fmt.Sprintf("Function '%s' modifies state but lacks access control", function.Name),
						Line:        function.Location.Line,
						Code:        fmt.Sprintf("function %s", function.Name),
						Category:    "smart_contract",
						CWE:         "CWE-284",
						Suggestion:  "Add appropriate access control modifiers (onlyOwner, onlyAdmin, etc.)",
						References:  []string{"https://docs.openzeppelin.com/contracts/4.x/access-control"},
					})
				}
			}
		}
	}

	return issues
}

// detectTimestampDependence detects timestamp dependence vulnerabilities
func (vd *VulnerabilityDetector) detectTimestampDependence(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	timestampPattern := regexp.MustCompile(`(?i)(block\.timestamp|now)\s*[<>=!]`)

	for i, line := range lines {
		if timestampPattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("timestamp_%d", i+1),
				Type:        "timestamp_dependence",
				Severity:    "medium",
				Title:       "Timestamp Dependence",
				Description: "Dangerous use of block.timestamp for critical logic",
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Category:    "smart_contract",
				CWE:         "CWE-829",
				Suggestion:  "Avoid using block.timestamp for critical logic; use block numbers or external oracles",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/recommendations/#timestamp-dependence"},
			})
		}
	}

	return issues
}

// detectDoS detects potential denial of service vulnerabilities
func (vd *VulnerabilityDetector) detectDoS(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	// Check for unbounded loops
	loopPattern := regexp.MustCompile(`(?i)(for\s*\(|while\s*\()`)
	arrayPattern := regexp.MustCompile(`(?i)\.length`)

	for i, line := range lines {
		if loopPattern.MatchString(line) && arrayPattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("dos_loop_%d", i+1),
				Type:        "denial_of_service",
				Severity:    "medium",
				Title:       "Potential DoS via Unbounded Loop",
				Description: "Loop over dynamic array may cause gas limit DoS",
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Category:    "smart_contract",
				CWE:         "CWE-400",
				Suggestion:  "Implement pagination or limit array size to prevent gas limit DoS",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/attacks/denial-of-service/"},
			})
		}
	}

	return issues
}

// detectFrontRunning detects front-running vulnerabilities
func (vd *VulnerabilityDetector) detectFrontRunning(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	pricePattern := regexp.MustCompile(`(?i)(price|rate|amount)\s*=`)

	for i, line := range lines {
		if pricePattern.MatchString(line) && strings.Contains(line, "public") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("frontrun_%d", i+1),
				Type:        "front_running",
				Severity:    "medium",
				Title:       "Potential Front-Running Vulnerability",
				Description: "Public price/rate setting may be vulnerable to front-running",
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Category:    "smart_contract",
				CWE:         "CWE-362",
				Suggestion:  "Use commit-reveal schemes or other anti-front-running mechanisms",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/attacks/frontrunning/"},
			})
		}
	}

	return issues
}

// detectUninitializedStorage detects uninitialized storage pointers
func (vd *VulnerabilityDetector) detectUninitializedStorage(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	storagePattern := regexp.MustCompile(`(?i)\w+\s+storage\s+\w+;`)

	for i, line := range lines {
		if storagePattern.MatchString(line) && !strings.Contains(line, "=") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("uninit_storage_%d", i+1),
				Type:        "uninitialized_storage",
				Severity:    "high",
				Title:       "Uninitialized Storage Pointer",
				Description: "Storage pointer declared but not initialized",
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Category:    "smart_contract",
				CWE:         "CWE-824",
				Suggestion:  "Initialize storage pointers or use memory instead",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/recommendations/#uninitialized-storage-pointers"},
			})
		}
	}

	return issues
}

// detectDelegatecallInjection detects delegatecall injection vulnerabilities
func (vd *VulnerabilityDetector) detectDelegatecallInjection(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	delegatePattern := regexp.MustCompile(`(?i)\.delegatecall\(`)

	for i, line := range lines {
		if delegatePattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("delegatecall_%d", i+1),
				Type:        "delegatecall_injection",
				Severity:    "critical",
				Title:       "Dangerous Delegatecall Usage",
				Description: "Delegatecall to user-controlled address can lead to complete contract takeover",
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Category:    "smart_contract",
				CWE:         "CWE-470",
				Suggestion:  "Avoid delegatecall to user-controlled addresses; use a whitelist if necessary",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/recommendations/#delegatecall"},
			})
		}
	}

	return issues
}

// detectSignatureMalleability detects signature malleability issues
func (vd *VulnerabilityDetector) detectSignatureMalleability(ast *ContractAST, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	ecrecoverPattern := regexp.MustCompile(`(?i)ecrecover\(`)

	for i, line := range lines {
		if ecrecoverPattern.MatchString(line) {
			// Check if there's proper signature validation
			if !strings.Contains(content, "OpenZeppelin") && !strings.Contains(content, "ECDSA") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("sig_malleability_%d", i+1),
					Type:        "signature_malleability",
					Severity:    "medium",
					Title:       "Potential Signature Malleability",
					Description: "Raw ecrecover usage without proper validation",
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Category:    "smart_contract",
					CWE:         "CWE-347",
					Suggestion:  "Use OpenZeppelin's ECDSA library for secure signature verification",
					References:  []string{"https://docs.openzeppelin.com/contracts/2.x/api/cryptography#ECDSA"},
				})
			}
		}
	}

	return issues
}

// Helper functions

func (vd *VulnerabilityDetector) containsStateChanges(content string, startLine int) bool {
	lines := strings.Split(content, "\n")
	stateChangePattern := regexp.MustCompile(`(?i)(=\s*[^=]|\.push\(|\.pop\(|delete\s+|transfer\(|send\()`)

	// Check a reasonable range around the function
	start := startLine - 1
	end := start + 20
	if end > len(lines) {
		end = len(lines)
	}

	for i := start; i < end; i++ {
		if stateChangePattern.MatchString(lines[i]) {
			return true
		}
	}

	return false
}

// detectWithRegex provides fallback detection using regex patterns
func (vd *VulnerabilityDetector) detectWithRegex(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Use the original scanner methods as fallback
	scanner := &Scanner{logger: vd.logger}

	issues = append(issues, scanner.checkReentrancy(filePath, content)...)
	issues = append(issues, scanner.checkIntegerOverflow(filePath, content)...)
	issues = append(issues, scanner.checkUncheckedCalls(filePath, content)...)
	issues = append(issues, scanner.checkAccessControl(filePath, content)...)
	issues = append(issues, scanner.checkGasOptimization(filePath, content)...)
	issues = append(issues, scanner.checkDeprecatedFunctions(filePath, content)...)

	return issues, nil
}
